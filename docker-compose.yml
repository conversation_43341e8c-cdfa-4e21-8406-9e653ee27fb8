services:
  app:
    build:
      context: .
      dockerfile: dev.Dockerfile
    ports:
      - '3000:3000'
    volumes:
      - ./src:/app/src
    #      - ./pnpm-lock.yaml:/app/pnpm-lock.yaml
    #      - /app/node_modules
    depends_on:
      - mongo
      - redis
    restart: unless-stopped
  mongo:
    image: mongo:latest
    container_name: webrtc_mongo
    ports:
      - '27017:27017'
    volumes:
      - mongo_data:/data/db

  redis:
    image: redis/redis-stack:latest
    container_name: webrtc_redis
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data

volumes:
  mongo_data:
  redis_data:
