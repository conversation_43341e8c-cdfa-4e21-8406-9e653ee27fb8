{"name": "cabiz-translate", "version": "1.0.0", "description": "websocket", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prepare": "husky || true", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:loca": "TZ=UTC nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/typeorm/cli"}, "dependencies": {"@deepgram/sdk": "^4.4.0", "@google-cloud/storage": "^7.14.0", "@google/genai": "^0.8.0", "@nestjs/axios": "^3.1.2", "@nestjs/common": "^10.3.9", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.3.9", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.0.5", "@nestjs/microservices": "^10.3.9", "@nestjs/mongoose": "^10.0.6", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.9", "@nestjs/platform-socket.io": "^10.3.9", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.3.9", "@roamhq/wrtc": "^0.8.0", "@socket.io/redis-adapter": "^8.3.0", "async-mutex": "^0.5.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.6", "cross-fetch": "^4.1.0", "dayjs": "^1.11.11", "events": "^3.3.0", "express-http-context": "^1.2.4", "fluent-ffmpeg": "^2.1.3", "groq-sdk": "^0.19.0", "husky": "^9.1.3", "ioredis": "^5.4.1", "lodash": "^4.17.21", "microsoft-cognitiveservices-speech-sdk": "^1.41.0", "mongodb": "^6.7.0", "mongoose": "^8.4.1", "node-fetch": "^3.3.2", "p-limit": "^6.2.0", "passport-jwt": "^4.0.1", "path": "^0.12.7", "redis": "^4.6.14", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.7.5", "stream": "^0.0.3", "typeorm": "^0.3.20", "uuid": "^11.0.3", "ws": "^8.18.0"}, "devDependencies": {"@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.1.1", "@nestjs/testing": "^10.3.9", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.7", "@types/express": "^4.17.21", "@types/fluent-ffmpeg": "^2.1.27", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.5", "@types/node": "^20.14.2", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.3.1", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "^29.1.4", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}