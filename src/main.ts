import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { AppConfig } from './app.config';
import { Transport } from '@nestjs/microservices';
import { RedisIoAdapter } from './share_modules/redis/redis-io.adapter';
import { RedisConnectionsKeeper } from './share_modules/redis/redis.connections.keeper';

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'debug', 'verbose', 'log'],
    bufferLogs: true,
  });

  const configService = app.get(ConfigService);

  const port = configService.get<AppConfig['APP_PORT']>('APP_PORT')!;

  app.enableCors();

  const redisHost = configService.get<AppConfig['REDIS_HOST']>('REDIS_HOST')!;
  const redisPort = configService.get<AppConfig['REDIS_PORT']>('REDIS_PORT')!;

  app.connectMicroservice({
    transport: Transport.REDIS,
    options: {
      host: redisHost,
      port: redisPort,
    },
  });

  const connectionsKeeper = app.get(RedisConnectionsKeeper);
  app.useWebSocketAdapter(new RedisIoAdapter(app, connectionsKeeper));

  await app.startAllMicroservices();

  await app.listen(port);
}

bootstrap().catch((error) => console.log(error));
