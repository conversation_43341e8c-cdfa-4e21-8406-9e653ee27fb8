import { nonstandard } from '@roamhq/wrtc';
import { Injectable } from '@nestjs/common';
import { RTCAudioSource as RTCAudioSourceType } from '@roamhq/wrtc/types/nonstandard';

export interface IRTCAudioSource extends RTCAudioSourceType {
  onBufferData(buffer: Buffer): void;
}

@Injectable()
export class RTCAudioSource extends nonstandard.RTCAudioSource {
  private readonly leftoverSamples: Int16Array;
  private numLeftoverSamples: number;
  private readonly numberOfSamplesPerFrame: number;

  constructor(public readonly sampleRate: number = 16000) {
    super();

    this.numberOfSamplesPerFrame = sampleRate / 100;
    this.leftoverSamples = new Int16Array(this.numberOfSamplesPerFrame);
    this.numLeftoverSamples = 0;
  }

  async onBufferData(buffer: Buffer, onDone: () => void, onError?: () => void) {
    const samples: Int16Array = new Int16Array(
      buffer.buffer,
      buffer.byteOffset,
      buffer.byteLength / Int16Array.BYTES_PER_ELEMENT,
    );

    let chunkStart = 0;

    while (chunkStart < samples.length) {
      const wantedNumberOfSamples =
        this.numberOfSamplesPerFrame - this.numLeftoverSamples;

      const remainingSamples = samples.length - chunkStart;

      if (remainingSamples < wantedNumberOfSamples) {
        this.leftoverSamples.set(samples.slice(chunkStart));
        this.numLeftoverSamples = remainingSamples;

        break;
      }

      let chunk = samples.slice(chunkStart, chunkStart + wantedNumberOfSamples);

      if (this.numLeftoverSamples) {
        this.leftoverSamples.set(chunk, this.numLeftoverSamples);

        chunk = this.leftoverSamples;

        this.numLeftoverSamples = 0;
      }

      // Gửi dữ liệu đến WebRTC
      this.onData({
        samples: chunk,
        numberOfFrames: this.numberOfSamplesPerFrame,
        sampleRate: this.sampleRate,
      });

      chunkStart += wantedNumberOfSamples;
    }

    onDone();
  }
}
