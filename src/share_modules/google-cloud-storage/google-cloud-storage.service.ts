import { Injectable } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';
import serviceAccount from './locamos-dev-0c166cd1ccc0.json';

@Injectable()
export class GoogleCloudStorageService {
  private storage: Storage;
  private readonly bucketName: string;

  constructor() {
    this.storage = new Storage({
      credentials: serviceAccount,
    });
    this.bucketName = 'cabiz-data-dev';
  }

  async uploadBuffer(
    buffer: Buffer,
    filename: string,
    contentType: string = 'audio/wav',
  ): Promise<string> {
    try {
      const bucket = this.storage.bucket(this.bucketName);
      const file = bucket.file(filename);

      await file.save(buffer, {
        contentType,
      });

      // Trả về URL tải file
      return `https://storage.googleapis.com/${this.bucketName}/${filename}`;
    } catch (error) {
      console.error('Lỗi khi tải lên file:', error);
      throw new Error('Không thể tải file lên Google Cloud Storage');
    }
  }
}
