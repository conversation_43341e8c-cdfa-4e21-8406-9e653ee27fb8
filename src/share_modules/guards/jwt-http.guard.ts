import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class JwtHttpGuard implements CanActivate {
  constructor(
    private readonly configService: ConfigService,
    private jwtService: JwtService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest();
      const authorization: string = request.headers['authorization'] || '';
      const token = authorization.split(' ').pop();

      const secret = this.configService.get<string>('JWT_SECRET');
      const expiresIn = this.configService.get<string>('JWT_EXPIRES_IN');

      await this.jwtService.verifyAsync(token, {
        secret: secret,
      });
    } catch (_) {
      throw new UnauthorizedException();
    }

    return true;
  }
}
