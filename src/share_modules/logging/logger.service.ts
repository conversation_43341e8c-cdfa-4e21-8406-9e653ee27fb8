import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Log } from './models/log.model';

@Injectable()
export class LoggerService {
  constructor(@InjectModel(Log.name) private logModel: Model<Log>) {}

  async log(level: string, message: string, data?: any) {
    const log = new this.logModel({ level, message, data });
    try {
      await log.save();
      console.log(`[${level.toUpperCase()}] - ${message}`);
    } catch (error) {
      console.error('Error saving log to MongoDB:', error);
    }
  }

  info(message: string, data?: any) {
    this.log('info', message, data);
  }

  warning(message: string, data?: any) {
    this.log('warning', message, data);
  }

  error(message: string, data?: any) {
    this.log('error', message, data);
  }
}
