import { Redis } from 'ioredis';
import { Injectable, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppConfig } from 'src/app.config';

@Injectable()
export class RedisConnections<PERSON>eeper implements OnModuleDestroy {
  private clients: Redis[] = [];

  constructor(private readonly configService: ConfigService) {}

  async onModuleDestroy(): Promise<void> {
    for (const client of this.clients) {
      await client.quit();
    }
  }

  createConnect(): Redis {
    const redisHost = this.configService.get<AppConfig['REDIS_HOST']>(
      'REDIS_HOST',
      'localhost',
    );
    const redisPort = this.configService.get<AppConfig['REDIS_PORT']>(
      'REDIS_PORT',
      6379,
    );

    const client = new Redis({
      host: redisHost,
      port: redisPort,
    });
    client.on('error', (err) => {
      console.log(`redis client error ${`${err.message}`}`);
    });

    this.clients.push(client);

    return client;
  }
}
