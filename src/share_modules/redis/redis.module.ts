import { Module, Global } from '@nestjs/common';
import Redis from 'ioredis';
import { RedisService } from './redis.service';
import { ConfigService } from '@nestjs/config';
import { AppConfig } from '../../app.config';
import { RedisConnectionsKeeper } from './redis.connections.keeper';

@Global()
@Module({
  providers: [
    RedisConnectionsKeeper,
    RedisService,
    {
      provide: 'REDIS_CLIENT',
      useFactory: (configService: ConfigService): Redis => {
        const host = configService.get<AppConfig['REDIS_HOST']>('REDIS_HOST')!;
        const port = configService.get<AppConfig['REDIS_PORT']>('REDIS_PORT')!;

        return new Redis({
          host: host,
          port: port,
        });
      },
      inject: [ConfigService],
    },
    {
      provide: 'REDIS_PUBLISHER_CLIENT',
      useFactory: (connectionKeeper: RedisConnectionsKeeper): Redis => {
        return connectionKeeper.createConnect();
      },
      inject: [RedisConnectionsKeeper],
    },
    {
      provide: 'REDIS_SUBSCRIBER_CLIENT',
      useFactory: (connectionKeeper: RedisConnectionsKeeper): Redis => {
        return connectionKeeper.createConnect();
      },
      inject: [RedisConnectionsKeeper],
    },
  ],
  exports: [
    'REDIS_CLIENT',
    'REDIS_PUBLISHER_CLIENT',
    'REDIS_SUBSCRIBER_CLIENT',
    RedisService,
  ],
})
export class RedisModule {}
