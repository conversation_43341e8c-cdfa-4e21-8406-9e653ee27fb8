import { Inject, Injectable } from '@nestjs/common';
import Redis from 'ioredis';

@Injectable()
export class RedisService {
  constructor(@Inject('REDIS_CLIENT') private readonly redisClient: Redis) {}

  getClient(): Redis {
    return this.redisClient;
  }

  setJson(doc: string, id: string, data: object, jsonPath?: string) {
    const path = jsonPath ? `$.${jsonPath}` : `$`;

    return this.getClient().call(
      'JSON.SET',
      `${doc}:${id}`,
      path,
      JSON.stringify(data),
    );
  }

  async getJson(doc: string, id: string): Promise<Record<string, any> | null> {
    const result = await this.getClient().call('JSON.GET', `${doc}:${id}`);

    if (result === null) {
      return null;
    }

    return typeof result === 'string' ? JSON.parse(result) : null;
  }

  deleteJson(doc: string, id: string) {
    return this.getClient().call('JSON.DEL', `${doc}:${id}`);
  }
  async get(key: string): Promise<string | null> {
    return this.getClient().get(key);
  }
  async set(key: string, value: string | number, ttl?: number): Promise<void> {
    if (ttl) {
      await this.getClient().set(key, value, 'EX', ttl);
    } else {
      await this.getClient().set(key, value);
    }
  }
}
