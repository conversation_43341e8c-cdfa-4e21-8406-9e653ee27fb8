import { createClient, DeepgramClient, LiveTranscriptionEvents } from "@deepgram/sdk";
import { Injectable } from "@nestjs/common";
import { PassThrough } from "stream";
const fetch = require("cross-fetch");


@Injectable()
export class AudioStreamDeepgramService {
  private deepgramClient: DeepgramClient;
  private keepAlive: NodeJS.Timeout;

  constructor() {
    if (!process.env.DEEPGRAM_API_KEY) {
      throw new Error("DEEPGRAM_API_KEY environment variable is not set.");
    }
    
    this.deepgramClient = createClient(process.env.DEEPGRAM_API_KEY);
  }

  startStreaming(
    peerConnection: RTCPeerConnection,
    audioSink: PassThrough,
    language: string,
  ) {
    const deepgram = this.deepgramClient.listen.live({
      language: language,
      punctuate: true,
      smart_format: true,
      model: "nova-2"
      // sample_rate: 48000,
      // channels: 1,
      // encoding: "mulaw",
    });

    // deepgram.conn.onmessage = (event: MessageEvent) => {
    //   const data: any = JSON.parse(event.data.toString());
    //   console.log(data);
    // }

    if (this.keepAlive) clearInterval(this.keepAlive);
    this.keepAlive = setInterval(() => {
      console.log("deepgram: keepalive");
      deepgram.keepAlive();
    }, 10 * 1000);

    audioSink.on("data", (chunk) => {
      if (
        deepgram.getReadyState() === 1 &&
        peerConnection.iceConnectionState === 'connected'
      ) {
        deepgram.send(chunk);
      }
    });

    deepgram.addListener(LiveTranscriptionEvents.Open, async () => {
      console.log("deepgram: connected");
      deepgram.addListener(LiveTranscriptionEvents.Transcript, (data) => {
        console.log("deepgram: packet received");
        console.log("deepgram: transcript received");
        console.log("socket: transcript sent to client");
        console.log(data.channel.alternatives[0].transcript);
      });

      deepgram.addListener(LiveTranscriptionEvents.Close, async (data) => {
        console.log("deepgram: disconnected");
        console.log(data);
        clearInterval(this.keepAlive);
      });

      deepgram.addListener(LiveTranscriptionEvents.Error, async (error) => {
        console.log("deepgram: error received");
        console.error(error);
      });

      // deepgram.addListener(LiveTranscriptionEvents.Warning, async (warning) => {
      //   console.log("deepgram: warning received");
      //   console.warn(warning);
      // });

      deepgram.addListener(LiveTranscriptionEvents.Metadata, (data) => {
        console.log("deepgram: packet received");
        console.log("deepgram: metadata received");
        console.log("ws: metadata sent to client");
        console.log({ metadata: data });
      });
    });

    return deepgram;
  };

  async testlive() {
    

    // URL for the real-time streaming audio you would like to transcribe
    const url = "http://stream.live.vc.bbcmedia.co.uk/bbc_world_service";

    // Create a websocket connection to Deepgram
    const connection = this.deepgramClient.listen.live({
      smart_format: true,
      model: 'nova-2',
      language: 'en-US',
    });

    // Listen for the connection to open.
    connection.on(LiveTranscriptionEvents.Open, () => {
      // Listen for any transcripts received from Deepgram and write them to the console.
      connection.on(LiveTranscriptionEvents.Transcript, (data) => {
        console.log(data.channel.alternatives[0].transcript);
      });

      // Listen for any metadata received from Deepgram and write it to the console.
      connection.on(LiveTranscriptionEvents.Metadata, (data) => {
        console.dir(data, { depth: null });
      });

      // Listen for the connection to close.
      connection.on(LiveTranscriptionEvents.Close, () => {
        console.log("Connection closed.");
      });

      // Send streaming audio from the URL to Deepgram.
      fetch(url)
        .then((r) => r.body)
        .then((res) => {
          res.on("readable", () => {
            connection.send(res.read());
          });
        });
    });
  };
}
