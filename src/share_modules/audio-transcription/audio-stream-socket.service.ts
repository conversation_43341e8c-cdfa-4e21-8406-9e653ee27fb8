import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { nonstandard } from '@roamhq/wrtc';
import { RTCAudioData as RTCAudioDataNonstandard } from '@roamhq/wrtc/types/nonstandard';
import WebSocket from 'ws';

export interface AudioStreamSocketOptions {
  socketUrl?: string;
  samplingRate?: number;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  sendInterval?: number; // Không sử dụng, luôn cố định là 1000ms (1 giây)
  channels?: number; // Số kênh audio (mặc định: 1 - mono)
  language?: string; // Ngôn ngữ của đoạn audio (mặc định: auto)
}

export interface AudioStreamSocketControl {
  stop: () => void;
}

@Injectable()
export class AudioStreamSocketService implements OnModuleDestroy {
  private readonly logger = new Logger(AudioStreamSocketService.name);
  private activeStreams = new Map<
    string,
    {
      socket: WebSocket;
      audioSink: nonstandard.RTCAudioSink;
      reconnectAttempts: number;
      reconnectTimer?: NodeJS.Timeout;
      sendInterval?: NodeJS.Timeout;
      samplingRate: number;
      channels: number;
    }
  >();

  constructor() {
    this.logger.log('AudioStreamSocketService initialized');
  }

  /**
   * Bắt đầu stream audio từ audioSink đến WebSocket server
   * @param streamId ID duy nhất cho stream
   * @param audioSink RTCAudioSink để nhận dữ liệu audio
   * @param options Tùy chọn cấu hình
   * @returns Đối tượng điều khiển stream
   */
  public startStreaming(
    streamId: string,
    audioSink: nonstandard.RTCAudioSink,
    options: AudioStreamSocketOptions,
    onMessage: (data: any) => void,
  ): AudioStreamSocketControl {
    if (this.activeStreams.has(streamId)) {
      this.logger.warn(`Stream với ID ${streamId} đã tồn tại. Dừng stream cũ.`);
      this.stopStreamingById(streamId);
    }

    const {
      socketUrl = 'wss://asr.tban.io.vn/asr',
      samplingRate = 48000,
      reconnectInterval = 5000,
      maxReconnectAttempts = 5,
      channels = 1,
      language = 'auto',
    } = options;

    this.logger.log(
      `Bắt đầu stream audio với ID: ${streamId} đến ${socketUrl}`,
    );

    // Khởi tạo WebSocket
    const socket = this.createWebSocket(
      streamId,
      socketUrl,
      audioSink,
      {
        reconnectInterval,
        maxReconnectAttempts,
        samplingRate,
        channels,
        language: language,
      },
      onMessage,
    );

    // Lưu thông tin stream
    this.activeStreams.set(streamId, {
      socket,
      audioSink,
      reconnectAttempts: 0,
      sendInterval: null,
      samplingRate,
      channels,
    });

    return {
      stop: () => this.stopStreamingById(streamId),
    };
  }

  /**
   * Tạo và cấu hình WebSocket
   */
  private createWebSocket(
    streamId: string,
    socketUrl: string,
    audioSink: nonstandard.RTCAudioSink,
    config: {
      reconnectInterval: number;
      maxReconnectAttempts: number;
      samplingRate: number;
      channels: number;
      language: string;
    },
    onMessage: (data: any) => void,
  ): WebSocket {
    try {
      const wsUrl = new URL(socketUrl);
      wsUrl.searchParams.set('language', config.language || 'auto');

      const socket = new WebSocket(wsUrl.toString());

      // Xử lý sự kiện mở kết nối
      socket.onopen = () => {
        this.logger.log(
          `WebSocket đã kết nối thành công cho stream ${streamId}`,
        );

        // Reset số lần thử kết nối lại
        const streamInfo = this.activeStreams.get(streamId);
        if (streamInfo) {
          streamInfo.reconnectAttempts = 0;
        }

        // Thiết lập xử lý audio
        this.setupAudioProcessing(streamId, audioSink, config);
      };

      // Xử lý sự kiện đóng kết nối
      socket.onclose = (event) => {
        this.logger.warn(
          `WebSocket đã đóng kết nối cho stream ${streamId}. Code: ${event.code}, Reason: ${event.reason}`,
        );

        // Thử kết nối lại nếu không phải do người dùng đóng
        const streamInfo = this.activeStreams.get(streamId);
        if (
          streamInfo &&
          streamInfo.reconnectAttempts < config.maxReconnectAttempts
        ) {
          streamInfo.reconnectAttempts++;
          this.logger.log(
            `Đang thử kết nối lại (${streamInfo.reconnectAttempts}/${config.maxReconnectAttempts}) sau ${config.reconnectInterval}ms...`,
          );

          streamInfo.reconnectTimer = setTimeout(() => {
            if (this.activeStreams.has(streamId)) {
              const newSocket = this.createWebSocket(
                streamId,
                socketUrl,
                audioSink,
                config,
                onMessage,
              );
              const currentStreamInfo = this.activeStreams.get(streamId);
              if (currentStreamInfo) {
                currentStreamInfo.socket = newSocket;
              }
            }
          }, config.reconnectInterval);
        } else {
          audioSink.stop();
          // Đã vượt quá số lần thử kết nối lại, dừng stream
          this.logger.warn(
            `Đã vượt quá số lần thử kết nối lại cho stream ${streamId}. Dừng stream.`,
          );
          this.stopStreamingById(streamId);
        }
      };

      socket.onerror = (error) => {
        this.logger.error(
          `Lỗi WebSocket cho stream ${streamId}: ${error.message || 'Unknown error'}`,
        );
      };

      socket.onmessage = (event) => {
        onMessage(JSON.parse(event.data as string));
      };

      return socket;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo WebSocket cho stream ${streamId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Dừng stream theo ID
   */
  public stopStreamingById(streamId: string): boolean {
    const streamInfo = this.activeStreams.get(streamId);
    if (!streamInfo) {
      return false;
    }

    this.logger.log(`Dừng stream audio với ID: ${streamId}`);

    // Hủy timer kết nối lại nếu có
    if (streamInfo.reconnectTimer) {
      clearTimeout(streamInfo.reconnectTimer);
    }

    // Hủy timer gửi dữ liệu nếu có
    if (streamInfo.sendInterval) {
      clearInterval(streamInfo.sendInterval);
    }

    // Đóng WebSocket
    if (
      streamInfo.socket &&
      streamInfo.socket.readyState !== WebSocket.CLOSED
    ) {
      try {
        streamInfo.socket.close();
      } catch (error) {
        this.logger.error(
          `Lỗi khi đóng WebSocket cho stream ${streamId}: ${error.message}`,
        );
      }
    }

    // Xóa stream khỏi danh sách
    this.activeStreams.delete(streamId);

    return true;
  }

  /**
   * Dừng tất cả các stream
   */
  public stopAllStreams(): void {
    this.logger.log(`Dừng tất cả ${this.activeStreams.size} stream audio`);

    for (const streamId of this.activeStreams.keys()) {
      this.stopStreamingById(streamId);
    }

    this.activeStreams.clear();
  }

  // Phương thức setupSendInterval đã được thay thế bằng setupAudioProcessing

  /**
   * Thiết lập xử lý audio từ audioSink và gửi dữ liệu PCM thô đến WebSocket server
   * @param streamId ID của stream
   * @param audioSink RTCAudioSink để nhận dữ liệu audio
   * @param config Cấu hình cho việc chuyển đổi
   */
  private setupAudioProcessing(
    streamId: string,
    audioSink: nonstandard.RTCAudioSink,
    config: {
      samplingRate: number;
      channels: number;
    },
  ): void {
    try {
      const streamInfo = this.activeStreams.get(streamId);
      if (!streamInfo) return;

      // Tạo một mảng buffer duy nhất để lưu trữ dữ liệu audio
      let audioBuffer: Buffer[] = [];

      // Thiết lập interval để gửi dữ liệu mỗi 1 giây
      const sendIntervalId = setInterval(() => {
        const socket = streamInfo.socket;

        if (
          socket &&
          socket.readyState === WebSocket.OPEN &&
          audioBuffer.length > 0
        ) {
          try {
            // Lấy tất cả buffer hiện tại bằng splice (lấy và xóa cùng lúc)
            const buffersToSend = audioBuffer.splice(0, audioBuffer.length);

            // Gộp tất cả buffer đã thu thập để gửi
            const combinedBuffer = Buffer.concat(buffersToSend);

            // Gửi dữ liệu PCM thô qua WebSocket
            socket.send(combinedBuffer, { binary: true });
          } catch (error) {
            this.logger.error(
              `Lỗi khi gửi buffer qua WebSocket: ${error.message}`,
            );
          }
        }
      }, 2000);

      // Lưu interval ID vào streamInfo để có thể dừng nó sau này
      streamInfo.sendInterval = sendIntervalId;

      // Thiết lập callback để nhận dữ liệu audio trực tiếp từ audioSink
      audioSink.ondata = (data: RTCAudioDataNonstandard) => {
        try {
          if (
            data.sampleRate === config.samplingRate &&
            data.channelCount === config.channels
          ) {
            audioBuffer.push(Buffer.from(data.samples.buffer));
          }
        } catch (error) {
          this.logger.error(`Lỗi khi xử lý dữ liệu audio: ${error.message}`);
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi thiết lập xử lý audio: ${error.message}`);
    }
  }

  onModuleDestroy(): void {
    this.stopAllStreams();
    this.logger.log('AudioStreamSocketService đã được hủy');
  }
}
