import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { ChildProcessWithoutNullStreams, spawn } from 'child_process';
import Groq from 'groq-sdk';
import pLimit from 'p-limit';
import * as fsPromises from 'fs/promises';
import * as fs from 'fs';
import * as path from 'path';
import { RTCAudioData as RTCAudioDataNonstandard } from '@roamhq/wrtc/types/nonstandard';
import { nonstandard } from '@roamhq/wrtc';
import { GoogleGenAI } from '@google/genai';

export interface AudioProcessingControl {
  stop: () => void;
}

@Injectable()
export class AudioTranscriptionService implements OnModuleDestroy {
  private readonly logger = new Logger(AudioTranscriptionService.name);
  private readonly groqClient: Groq;
  private readonly googleGenAIClient: GoogleGenAI;

  private readonly SEGMENT_DURATION = 6;
  private readonly MAX_SILENCE = 1;
  private readonly SILENCE_THRESHOLD = '-30dB';
  private readonly CONCURRENT_GROQ_REQUESTS = 5;
  private readonly TEMP_AUDIO_DIR = path.join('/tmp', 'temp_audio_segments');

  private readonly groqLimiter = pLimit(this.CONCURRENT_GROQ_REQUESTS);
  private activeProcesses = new Map<string, ChildProcessWithoutNullStreams>();

  constructor() {
    if (!process.env.GROQ_API_KEY) {
      this.logger.error('GROQ_API_KEY environment variable is not set.');
      throw new Error('GROQ_API_KEY environment variable is not set.');
    }
    this.groqClient = new Groq({ apiKey: process.env.GROQ_API_KEY });

    if (!process.env.GOOGLE_GEN_AI_API_KEY) {
      this.logger.error(
        'GOOGLE_GEN_AI_API_KEY environment variable is not set.',
      );
      throw new Error('GOOGLE_GEN_AI_API_KEY environment variable is not set.');
    }
    this.googleGenAIClient = new GoogleGenAI({
      apiKey: process.env.GOOGLE_GEN_AI_API_KEY,
    });

    this.logger.log(
      `Initialized AudioTranscriptionService. Concurrent Groq request limit: ${this.CONCURRENT_GROQ_REQUESTS}`,
    );

    this.ensureTempDirExists(this.TEMP_AUDIO_DIR);
  }

  private async ensureTempDirExists(dir: string): Promise<void> {
    try {
      await fsPromises.mkdir(dir, { recursive: true });
      this.logger.log(`Created root temporary directory: ${dir}`);
    } catch (error) {
      this.logger.error(
        `Failed to create root temporary directory ${dir}:`,
        error,
      );
      throw error;
    }
  }

  public startProcessing(
    externalProcessId: string,
    audioSink: nonstandard.RTCAudioSink,
    onTranscription: (text: string, segments: [], language: string) => void,
    languague?: string,
  ): AudioProcessingControl {
    const processId = externalProcessId.trim();
    if (!processId) {
      this.logger.error(
        'Attempted to start processing with invalid externalProcessId.',
      );
      throw new Error(
        'Invalid or missing externalProcessId provided to startProcessing.',
      );
    }

    if (this.activeProcesses.has(processId)) {
      this.logger.warn(
        `[${processId}] Process ID already exists. Stopping the old process.`,
      );
      this.stopProcessingById(processId);
    }

    this.logger.log(
      `Starting new audio processing stream with ID: ${processId}`,
    );

    const processTempDir = path.join(this.TEMP_AUDIO_DIR, processId);
    this.ensureTempDirExists(processTempDir).catch((error) => {
      throw new Error(
        `[${processId}] Failed to create temporary directory: ${error.message}`,
      );
    });

    let ffmpegProcess: ChildProcessWithoutNullStreams | null = null;
    const segmentInterval: NodeJS.Timeout | null = null;
    let isStopping = false;
    let lastWrittenSegmentPath: string | null = null;
    const currentlyProcessingPaths = new Set<string>();
    const queuedSegments = new Set<string>(); // Track segments queued for processing

    const cleanup = () => {
      if (!ffmpegProcess) return;
      const pid = ffmpegProcess.pid;
      this.logger.log(`[${processId}] Starting resource cleanup (PID: ${pid})`);

      if (segmentInterval) clearInterval(segmentInterval);
      ffmpegProcess.stderr?.removeAllListeners();
      ffmpegProcess.removeAllListeners('exit');
      ffmpegProcess.removeAllListeners('error');
      ffmpegProcess.stdin?.removeAllListeners('error');
      audioSink.ondata = () => {};

      if (
        lastWrittenSegmentPath &&
        !currentlyProcessingPaths.has(lastWrittenSegmentPath) &&
        !queuedSegments.has(lastWrittenSegmentPath)
      ) {
        this.triggerSegmentProcessing(
          lastWrittenSegmentPath,
          onTranscription,
          processId,
          currentlyProcessingPaths,
          queuedSegments,
          languague,
        );
      }

      this.activeProcesses.delete(processId);
      setTimeout(() => {
        fsPromises
          .rm(processTempDir, { recursive: true, force: true })
          .catch((err) => {
            this.logger.error(
              `[${processId}] Error deleting temp directory ${processTempDir}:`,
              err,
            );
          });
      }, 500);
    };

    const stopProcessingInternal = () => {
      if (isStopping || !ffmpegProcess || ffmpegProcess.killed) return;
      isStopping = true;
      const pid = ffmpegProcess.pid;
      this.logger.log(
        `[${processId}] Received request to stop processing stream (PID: ${pid})`,
      );

      audioSink.ondata = () => {};
      this.logger.debug(`[${processId}] Disconnected audio sink.`);

      if (ffmpegProcess.stdin && !ffmpegProcess.stdin.destroyed) {
        ffmpegProcess.stdin.end((err) => {
          if (err && (err as NodeJS.ErrnoException).code !== 'EPIPE') {
            this.logger.error(
              `[${processId}] Error closing FFmpeg stdin:`,
              err,
            );
          }
        });
      }

      const killTimeout = setTimeout(() => {
        if (ffmpegProcess && !ffmpegProcess.killed) {
          this.logger.warn(
            `[${processId}] Forcing FFmpeg (PID: ${pid}) to stop (SIGKILL)...`,
          );
          ffmpegProcess.kill('SIGKILL');
        }
      }, 2000);

      ffmpegProcess.once('exit', () => {
        clearTimeout(killTimeout);
        cleanup();
      });
    };

    try {
      const segmentFilenamePattern = path.join(
        processTempDir,
        'segment_%05d.wav',
      );
      ffmpegProcess = spawn('ffmpeg', [
        '-f',
        's16le',
        '-ar',
        '48000',
        '-ac',
        '1',
        '-i',
        'pipe:0',
        '-af',
        `loudnorm=I=-16:LRA=11:TP=-1.5,aresample=resampler=soxr,silencedetect=noise=${this.SILENCE_THRESHOLD}:d=${this.MAX_SILENCE}`,
        '-f',
        'segment',
        '-segment_time',
        this.SEGMENT_DURATION.toString(),
        '-reset_timestamps',
        '1',
        '-c:a',
        'pcm_s16le',
        '-ar',
        '16000',
        '-ac',
        '1',
        segmentFilenamePattern,
      ]);

      this.activeProcesses.set(processId, ffmpegProcess);
      this.logger.log(
        `[${processId}] FFmpeg process (PID: ${ffmpegProcess.pid}) spawned. Total active: ${this.activeProcesses.size}`,
      );

      ffmpegProcess.stdin.on('error', (err) => {
        if ((err as NodeJS.ErrnoException).code !== 'EPIPE') {
          this.logger.error(
            `[${processId}] Error writing to FFmpeg stdin (PID: ${ffmpegProcess.pid}):`,
            err,
          );
        }
      });

      audioSink.ondata = (data: RTCAudioDataNonstandard) => {
        if (ffmpegProcess && !ffmpegProcess.stdin.destroyed) {
          const buffer = Buffer.from(data.samples.buffer);
          if (!ffmpegProcess.stdin.write(buffer)) {
            this.logger.warn(
              `[${processId}] Backpressure detected when writing to FFmpeg stdin.`,
            );
          }
        }
      };

      ffmpegProcess.stderr.on('data', (data) => {
        const output = data.toString();
        const openingMatch = output.match(/Opening '([^']+)' for writing/);
        if (openingMatch) {
          const newSegmentPath = openingMatch[1];
          this.logger.log(
            `[${processId}] FFmpeg started writing file: ${path.basename(newSegmentPath)}`,
          );

          // Only process the previous segment when a new one starts, if it’s not already queued or processing
          if (
            lastWrittenSegmentPath &&
            lastWrittenSegmentPath !== newSegmentPath &&
            !currentlyProcessingPaths.has(lastWrittenSegmentPath) &&
            !queuedSegments.has(lastWrittenSegmentPath)
          ) {
            this.triggerSegmentProcessing(
              lastWrittenSegmentPath,
              onTranscription,
              processId,
              currentlyProcessingPaths,
              queuedSegments,
              languague,
            );
          }
          lastWrittenSegmentPath = newSegmentPath;
        }
      });

      ffmpegProcess.on('exit', (code, signal) => {
        this.logger.log(
          `[${processId}] FFmpeg process (PID: ${ffmpegProcess.pid}) exited with code ${code}, signal ${signal}`,
        );
        cleanup();
      });

      ffmpegProcess.on('error', (error) => {
        this.logger.error(
          `[${processId}] FFmpeg process (PID: ${ffmpegProcess.pid}) encountered an error:`,
          error,
        );
        cleanup();
      });
    } catch (error) {
      this.logger.error(
        `[${processId}] Failed to initialize FFmpeg or set up processing:`,
        error,
      );
      stopProcessingInternal();
      fsPromises
        .rm(processTempDir, { recursive: true, force: true })
        .catch(() => {});
      this.activeProcesses.delete(processId);
      throw new Error(`[${processId}] Could not start FFmpeg process.`);
    }

    return { stop: stopProcessingInternal };
  }

  private triggerSegmentProcessing(
    filePath: string,
    callback: (text: string, segments: [], language: string) => void,
    processId: string,
    processingSet: Set<string>,
    queuedSet: Set<string>,
    language?: string,
  ): void {
    if (!filePath || processingSet.has(filePath) || queuedSet.has(filePath)) {
      this.logger.debug(
        `[${processId}] Skipping trigger for ${path.basename(filePath)}: already processing or queued`,
      );
      return;
    }
    queuedSet.add(filePath);

    setTimeout(async () => {
      try {
        const stats = await fsPromises.stat(filePath);
        if (!stats.isFile() || stats.size < 1024) {
          this.logger.warn(
            `[${processId}] File ${path.basename(filePath)} not ready or too small (${stats.size} bytes), skipping`,
          );
          queuedSet.delete(filePath);
          return;
        }
        await this.processSegment(
          filePath,
          callback,
          processId,
          processingSet,
          language,
        );
      } catch (err) {
        if (err.code === 'ENOENT') {
          this.logger.warn(
            `[${processId}] File ${path.basename(filePath)} not found, likely already processed or deleted`,
          );
        } else {
          this.logger.error(
            `[${processId}] Unexpected error processing file ${path.basename(filePath)}:`,
            err,
          );
        }
      } finally {
        queuedSet.delete(filePath);
      }
    }, 1000); // Increased delay to ensure file is fully written
  }

  private async processSegment(
    filePath: string,
    callback: (text: string, segments: [], language: string) => void,
    processId: string,
    processingSet: Set<string>,
    language?: string,
  ): Promise<void> {
    processingSet.add(filePath);
    try {
      const stats = await fsPromises.stat(filePath);
      if (stats.size < 1024) {
        throw new Error(
          `File too small (${stats.size} bytes), skipping: ${path.basename(filePath)}`,
        );
      }

      const transcriptionInfo = await this.groqLimiter(() =>
        this.transcribeAudio(filePath, processId, language),
      );

      let textSegments = '';

      for (const segment of transcriptionInfo.segments) {
        const textResultLowerCase = segment.text.toLowerCase();
        if (
          // textResultLowerCase.includes('cảm ơn các bạn đã theo dõi') ||
          // textResultLowerCase.includes('ghiền mì gõ') ||
          // textResultLowerCase.includes('hãy subscribe cho kênh') ||
          // textResultLowerCase.includes('hẹn gặp lại các bạn trong những video') ||
          textResultLowerCase.includes('ừ ừ') ||
          textResultLowerCase.includes('model itself')
        ) {
          continue;
        }

        const detect = this.detectHumanSpeech(segment);

        console.log({
          detect,
          text: segment.text,
          aa: {
            avg_logprob: segment.avg_logprob,
            temperature: segment.temperature,
            compression_ratio: segment.compression_ratio,
            no_speech_prob: segment.no_speech_prob,
          },
        });

        if (!detect.isHumanSpeech) {
          continue;
        }

        textSegments += segment.text;
      }

      const textResult = textSegments;

      if (textResult) {
        this.logger.error(textResult);
        const response = await this.googleGenAIClient.models.generateContent({
          model: 'gemini-2.0-flash-lite',
          config: {
            systemInstruction:
              'Please add punctuation to the following text and return the complete, punctuated version and only return the edited sentence',
          },
          contents: textResult,
        });

        this.logger.error(textResult);

        callback(
          response.text,
          transcriptionInfo.segments,
          transcriptionInfo.language,
        );
        this.logger.warn(response.text);
        this.logger.log(
          `[${processId}] Transcription succeeded for ${path.basename(filePath)}: "${response.text.substring(0, 50)}..."`,
        );
      } else {
        callback('', [], language);
        this.logger.log(
          `[${processId}] Transcription completed for ${path.basename(filePath)} but returned empty text`,
        );
      }
    } catch (error) {
      this.logger.error(
        `[${processId}] Transcription failed for file ${path.basename(filePath)}:`,
        error,
      );
    } finally {
      await fsPromises.unlink(filePath).catch((err) => {
        if (err.code !== 'ENOENT') {
          this.logger.error(
            `[${processId}] Error deleting file ${path.basename(filePath)}:`,
            err,
          );
        }
      });
      processingSet.delete(filePath);
    }
  }

  private async transcribeAudio(
    filePath: string,
    processId: string,
    language?: string,
  ): Promise<any> {
    const fileStream = fs.createReadStream(filePath);

    try {
      return await this.groqClient.audio.transcriptions.create({
        file: fileStream,
        model: 'whisper-large-v3',
        response_format: 'verbose_json',
        language: language,
        temperature: 0.0,
        prompt:
          'If the accuracy is low or the sentences are generated by the model itself due to training do not use',
      });
    } catch (error) {
      this.logger.error(
        `[${processId}] Transcription request to Groq failed for file ${path.basename(filePath)}:`,
        error,
      );
      throw error;
    } finally {
      fileStream.destroy();
    }
  }

  public stopProcessingById(processId: string): boolean {
    const process = this.activeProcesses.get(processId);
    const processTempDir = path.join(this.TEMP_AUDIO_DIR, processId);
    if (process && !process.killed) {
      process.stdin?.end();
      setTimeout(() => {
        if (!process.killed) process.kill('SIGKILL');
      }, 500);
      this.activeProcesses.delete(processId);
      fsPromises
        .rm(processTempDir, { recursive: true, force: true })
        .catch((err) => {
          this.logger.error(
            `[${processId}] Error deleting temp directory on stop:`,
            err,
          );
        });
      return true;
    }
    this.activeProcesses.delete(processId);
    return false;
  }

  public onModuleDestroy(): void {
    this.activeProcesses.forEach((proc, processId) => {
      if (!proc.killed) proc.kill('SIGKILL');
      const processTempDir = path.join(this.TEMP_AUDIO_DIR, processId);
      fsPromises
        .rm(processTempDir, { recursive: true, force: true })
        .catch((err) => {
          this.logger.error(
            `[${processId}] Error deleting temp directory on module destroy:`,
            err,
          );
        });
    });
    this.activeProcesses.clear();
    this.logger.log(
      'Module destroyed. All processes and temp directories cleaned up.',
    );
  }

  public stopAllProcessing(): void {
    this.activeProcesses.forEach((_, processId) =>
      this.stopProcessingById(processId),
    );
    this.activeProcesses.clear();
    this.logger.log('All processing streams stopped.');
  }

  detectHumanSpeech(segment) {
    // Lấy các giá trị từ segment
    const noSpeechProb = segment.no_speech_prob;
    const avgLogProb = segment.avg_logprob;
    const compressionRatio = segment.compression_ratio;
    const temperature = segment.temperature;

    // Định nghĩa ngưỡng
    const NO_SPEECH_THRESHOLD = 0.23; // Dưới 0.1: chắc chắn là giọng nói
    const AVG_LOGPROB_THRESHOLD = -0.5; // Trên -0.2: độ tin cậy cao
    const COMPRESSION_MIN = 0.9; // Tối thiểu cho giọng nói tự nhiên
    const COMPRESSION_MAX = 3.0; // Tối đa cho giọng nói tự nhiên
    const TEMPERATURE = 0.4; // Độ chính xác

    // Kiểm tra từng điều kiện
    const isLowNoSpeechProb = noSpeechProb < NO_SPEECH_THRESHOLD;
    const isHighConfidence = avgLogProb > AVG_LOGPROB_THRESHOLD;
    const isNaturalCompression =
      compressionRatio >= COMPRESSION_MIN &&
      compressionRatio <= COMPRESSION_MAX;
    const isTemperature = temperature < TEMPERATURE;

    console.log({
      isLowNoSpeechProb,
      isHighConfidence,
      isNaturalCompression,
      isTemperature,
    });

    // Quyết định dựa trên đa số điều kiện
    const conditionsMet = [
      isLowNoSpeechProb,
      isHighConfidence,
      isNaturalCompression,
      isTemperature,
    ].filter(Boolean).length;

    // Trả về kết quả
    if (conditionsMet >= 3) {
      return {
        isHumanSpeech: true,
        reason:
          'Đoạn âm thanh đáp ứng ít nhất 2/3 tiêu chí: có khả năng là giọng nói con người.',
      };
    } else {
      return {
        isHumanSpeech: false,
        reason: `Đoạn âm thanh không đáp ứng đủ tiêu chí. Chi tiết: no_speech_prob=${noSpeechProb}, avg_logprob=${avgLogProb}, compression_ratio=${compressionRatio}`,
      };
    }
  }
}
