export const splitAddPunctuation = (text: string): any[] => {
  text = text.replace(/(\d)[.,](?=\d)/g, (match, p1) => {
    return match.includes('.') ? `${p1}_dot_` : `${p1}_comma_`;
  });
  const sp = text.split('');
  if (!'.!?'.includes(sp[sp.length - 1])) {
    text = `${text}.`;
  }
  const segments = [];
  const regex = /([^.!?]+)([.!?]+)/g;
  let match;
  while ((match = regex.exec(text)) !== null) {
    const str = match[1].trim();
    if (/^[.!?]+$/.test(str) === false) {
      segments.push({
        text: str,
        punctuation: match[2].trim(),
      });
    }
  }

  if (segments.length == 0) {
    return [
      {
        text: text,
        punctuation: '',
      },
    ];
  }

  for (let i = 0; i < segments.length; i++) {
    const item = segments[i];
    segments[i] = {
      ...item,
      text: segments[i]['text']
        .replaceAll('_dot_', '.')
        .replaceAll('_comma_', ','),
    };
  }
  return segments;
};

export const generateUUID = () => {
  // Public Domain/MIT
  let d = new Date().getTime(); //Timestamp
  let d2 =
    (typeof performance !== 'undefined' &&
      performance.now &&
      performance.now() * 1000) ||
    0; //Time in microseconds since page-load or 0 if unsupported
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16; //random number between 0 and 16
    if (d > 0) {
      //Use timestamp until depleted
      r = (d + r) % 16 | 0;
      d = Math.floor(d / 16);
    } else {
      //Use microseconds since page-load if supported
      r = (d2 + r) % 16 | 0;
      d2 = Math.floor(d2 / 16);
    }
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
};
