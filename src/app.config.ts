import { IsNumber, IsString, validateSync } from 'class-validator';
import { Type, plainToInstance } from 'class-transformer';

export class AppConfig {
  @IsString()
  readonly NODE_ENV: 'production' | 'stage' | 'development';

  @IsNumber()
  readonly APP_PORT: number;

  @IsString()
  readonly APP_URL: string;

  @IsString()
  readonly JWT_SECRET: string = 'sdh7ZAl6IwEUY8y5z75gk';

  @IsString()
  readonly JWT_EXPIRES_IN: string = '2y';

  @IsString()
  readonly MONGODB_URI: string;

  @IsString()
  readonly STT_REALTIME_WS_URL: string = 'wss://asr.tban.io.vn';

  @IsString()
  readonly REDIS_HOST: string = 'localhost';

  @IsNumber()
  @Type(() => Number)
  readonly REDIS_PORT: number = 6379;
}

export function validateAppConfig(config: Record<string, unknown>): AppConfig {
  const validatedConfig = plainToInstance(AppConfig, config, {
    enableImplicitConversion: true,
  });
  const errors = validateSync(validatedConfig, {
    skipMissingProperties: false,
  });

  if (errors.length > 0) {
    throw new Error(errors.toString());
  }
  return validatedConfig;
}
