import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppConfig, validateAppConfig } from './app.config';
import { MongooseModule } from '@nestjs/mongoose';
import { RedisModule } from './share_modules/redis/redis.module';
import { TranslateModule } from './modules/translate/translate.module';
import { AzureModule } from './modules/azure/azure.module';
import { LoggerModule } from './share_modules/logging/logger.module';
import { GoogleCloudStorageModule } from './share_modules/google-cloud-storage/google-cloud-storage.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AudioTranscriptionModule } from './share_modules/audio-transcription/audio-transcription.module';

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
      validate: validateAppConfig,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        return {
          uri: configService.get<AppConfig['MONGODB_URI']>('MONGODB_URI'),
          autoCreate: true,
          autoIndex: true,
        };
      },
      inject: [ConfigService],
    }),
    RedisModule,
    TranslateModule,
    LoggerModule,
    AzureModule,
    GoogleCloudStorageModule,
    AudioTranscriptionModule,
  ],
})
export class AppModule {}
