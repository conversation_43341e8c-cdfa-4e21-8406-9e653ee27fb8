import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { WrtcConfig } from './entities/wrtc-config.model';
import { Model } from 'mongoose';

@Injectable()
export class TranslateService {
  constructor(
    @InjectModel(WrtcConfig.name) private wrtcConfigModel: Model<WrtcConfig>,
  ) {}

  async getIceServers(): Promise<RTCIceServer[]> {
    const iceServers = await this.wrtcConfigModel.findOne({
      name: 'iceServers',
    });

    if (
      !iceServers ||
      !Array.isArray(iceServers.value) ||
      iceServers.value.length === 0
    ) {
      return [];
    }

    return iceServers.value as RTCIceServer[];
  }
}
