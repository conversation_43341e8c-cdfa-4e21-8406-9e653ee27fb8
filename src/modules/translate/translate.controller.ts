import { Controller, Get, UseFilters, UseGuards } from '@nestjs/common';
import { TranslateService } from './translate.service';
import { JwtHttpGuard } from '../../share_modules/guards/jwt-http.guard';
import { BaseExceptionFilter } from '@nestjs/core';

// @UseFilters(BaseExceptionFilter)
// @UseGuards(JwtHttpGuard)
@Controller('wrtc')
export class TranslateController {
  constructor(private translateService: TranslateService) {}

  @Get('ice-servers')
  getIceServers(): Promise<RTCIceServer[]> {
    return this.translateService.getIceServers();
  }
}
