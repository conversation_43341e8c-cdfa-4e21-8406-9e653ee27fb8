import { Module } from '@nestjs/common';
import { SignalingGateway } from './translate.gateway';
import { AzureModule } from '../azure/azure.module';
import { MongooseModule } from '@nestjs/mongoose';
import { RedisModule } from '../../share_modules/redis/redis.module';
import { LoggerModule } from '../../share_modules/logging/logger.module';
import { WrtcConfig, WrtcConfigSchema } from './entities/wrtc-config.model';
import { TranslateController } from './translate.controller';
import { TranslateService } from './translate.service';
import { GoogleCloudStorageModule } from '../../share_modules/google-cloud-storage/google-cloud-storage.module';
import { BufferQueueService } from './queues/buffer-queue.service';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppConfig } from '../../app.config';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: WrtcConfig.name,
        schema: WrtcConfigSchema,
      },
    ]),
    RedisModule,
    LoggerModule,
    AzureModule,
    GoogleCloudStorageModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const secret =
          configService.get<AppConfig['JWT_SECRET']>('JWT_SECRET')!;
        const expiresIn =
          configService.get<AppConfig['JWT_EXPIRES_IN']>('JWT_EXPIRES_IN')!;

        return {
          secret: secret,
          signOptions: {
            expiresIn,
          },
          verifyOptions: {
            ignoreExpiration: false,
          },
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [SignalingGateway, TranslateService, BufferQueueService],
  controllers: [TranslateController],
})
export class TranslateModule {}
