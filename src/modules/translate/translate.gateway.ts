import {
  SubscribeMessage,
  WebSocketGateway,
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import {
  RTCPeerConnection,
  RTCSessionDescription,
  RTCIceCandidate,
  nonstandard,
} from '@roamhq/wrtc';

import { RTCAudioData as RTCAudioDataNonstandard } from '@roamhq/wrtc/types/nonstandard';
import * as sdk from 'microsoft-cognitiveservices-speech-sdk';
import { RTCAudioSource } from '../../share_modules/wrtc/RTCAudioSource';
import {
  AudioFormatTag,
  AudioStreamFormat,
} from 'microsoft-cognitiveservices-speech-sdk';
import { spawn } from 'node:child_process';
import { SpeechService } from '../azure/speech.service';
import { LoggerService } from '../../share_modules/logging/logger.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WrtcConfig } from './entities/wrtc-config.model';
import { BufferQueueService } from './queues/buffer-queue.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AudioFormatPayload } from './audio.interface';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PassThrough } from 'node:stream';

@WebSocketGateway({ cors: true })
export class SignalingGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private peerConnections = new Map<string, RTCPeerConnection>();
  private audioSources = new Map<string, RTCAudioSource>();
  private iceServers: RTCIceServer[] = [];

  constructor(
    private readonly configService: ConfigService,
    private jwtService: JwtService,
    private readonly eventEmitter: EventEmitter2,
    private readonly bufferQueueService: BufferQueueService,
    private speechService: SpeechService,
    private loggerService: LoggerService,
    @InjectModel(WrtcConfig.name) private wrtcConfigModel: Model<WrtcConfig>,
  ) {
    this.initIceServers().catch(() => {
      this.loggerService.info('iceServers', {
        message: `Server: initIceServers failed`,
      });
    });
  }

  async initIceServers() {
    const iceServers = await this.wrtcConfigModel.findOne({
      name: 'iceServers',
    });
    if (
      !iceServers ||
      !Array.isArray(iceServers.value) ||
      iceServers.value.length === 0
    ) {
      this.loggerService.info('iceServers', {
        message: `Server: Notfound ice-servers in database`,
      });
    }

    this.iceServers = iceServers.value as unknown as RTCIceServer[];
  }

  async handleConnection(client: Socket) {
    try {
      // const authorization: string = client.handshake?.auth?.token || '';

      // const [, token] = authorization.split(' ');
      // const secret = this.configService.get<string>('JWT_SECRET');

      // await this.jwtService.verifyAsync(token, {
      //   secret: secret,
      // });

      this.loggerService.info(`Client connected: ${client.id}`);
      await this.createPeerConnection(client);

      client.emit('auth-status', {
        code: 200,
        message: 'Success',
      });
    } catch (e) {
      console.log(e);
      client.emit('auth-status', {
        message: 'Unauthorized',
        code: 403,
      });
      client.emit('error', {
        message: 'Unauthorized',
        code: 403,
      });
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket): Promise<void> {
    try {
      this.loggerService.info(`Client disconnected: ${client.id}`, {
        socketId: client.id,
      });

      this.eventEmitter.emit('PeerDisconnect', {
        socketId: client.id,
      });

      // Khi client ngắt kết nối, kiểm tra và dừng RTCPeerConnection
      const peerConnection = this.peerConnections.get(client.id);
      if (peerConnection) {
        this.loggerService.info(
          `Stopping peer connection and tracks ${client.id}`,
          {
            socketId: client.id,
          },
        );
        this.stopPeerConnection(peerConnection);
        this.peerConnections.delete(client.id);
        this.audioSources.delete(client.id);
      }
    } catch (e) {
      this.loggerService.error('handleDisconnect', {
        stack: e.stack,
        socketId: client.id,
      });
    }
  }

  async createPeerConnection(client: Socket) {
    this.initIceServers().catch(() => {
      this.loggerService.info('iceServers', {
        message: `Server: initIceServers failed`,
      });
    });

    // Tạo RTCPeerConnection
    const peerConnection = new RTCPeerConnection({
      iceServers: this.iceServers,
    });

    // Lưu peerConnection vào Map với client.id làm khóa
    this.peerConnections.set(client.id, peerConnection);

    peerConnection.onicecandidate = (event): void => {
      if (event.candidate) {
        this.loggerService.info('onicecandidate', {
          message: `Server: Emit ice-candidate`,
          socketId: client.id,
          candidate: event.candidate,
        });
        client.emit('ice-candidate', { candidate: event.candidate });
      }
    };

    // Lắng nghe sự kiện thay đổi trạng thái ICE để phát hiện ngắt kết nối
    peerConnection.addEventListener('iceconnectionstatechange', () => {
      this.loggerService.info('iceconnectionstatechange', {
        message: `ICE connection state: ${peerConnection.iceConnectionState}`,
        iceConnectionState: peerConnection.iceConnectionState,
        socketId: client.id,
      });
      if (
        peerConnection.iceConnectionState === 'disconnected' ||
        peerConnection.iceConnectionState === 'failed' ||
        peerConnection.iceConnectionState === 'closed'
      ) {
        this.loggerService.info('peerConnectionDisconnect', {
          message: 'Peer connection has been lost. Stopping all tracks.',
          iceConnectionState: peerConnection.iceConnectionState,
          socketId: client.id,
        });
        this.stopPeerConnection(peerConnection);
        this.peerConnections.delete(client.id);
        this.audioSources.delete(client.id);
      }
    });
  }

  @SubscribeMessage('offer')
  async handleOffer(
    client: Socket,
    payload: {
      offer: RTCSessionDescription;
      languageInput: string;
      languageOutput?: string;
      translateOutputType?: 'TRANSCRIPT' | 'TRANSLATE' | 'AUDIO_TRANSLATE';
      audioFormat?: AudioFormatPayload;
      forwardAudio?: boolean;
    },
  ) {
    const {
      languageInput,
      languageOutput,
      offer,
      translateOutputType = 'TRANSCRIPT',
      audioFormat = {
        samplesPerSecond: 8000,
      },
      forwardAudio = false,
    } = payload;

    try {
      this.loggerService.info('offer', {
        message: `Server: Received offer from client ${client.id}`,
        socketId: client.id,
        payload: payload,
      });

      if (!payload.languageInput) {
        this.loggerService.info('offer', {
          message: `payload.languageInput invalid`,
          socketId: client.id,
          payload: payload,
        });

        client.emit('error', {
          message: 'payload.languageInput invalid',
          code: 400,
        });
        return;
      }

      const peerConnection = this.peerConnections.get(client.id);
      if (!peerConnection) {
        this.loggerService.info('offer', {
          message: `Server: No peerConnection found for this client ${client.id}`,
          socketId: client.id,
          payload: payload,
        });
        return;
      }

      peerConnection.ontrack = async (event): Promise<void> =>
        this.peerConnectionOnTrack(
          client,
          peerConnection,
          event,
          languageInput,
          languageOutput,
          audioFormat,
          translateOutputType,
          forwardAudio,
        );

      // Thiết lập remote description với offer từ client
      await peerConnection.setRemoteDescription(
        new RTCSessionDescription(offer),
      );

      // Tạo answer và gửi cho client
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);
      this.loggerService.info('answer', {
        message: 'Server: Sending answer to client',
        socketId: client.id,
      });
      client.emit('answer', { answer });
    } catch (e) {
      this.loggerService.error('offer', {
        stack: e.stack,
        socketId: client.id,
        payload: payload,
      });
    }
  }

  async peerConnectionOnTrack(
    client: Socket,
    peerConnection: RTCPeerConnection,
    event: RTCTrackEvent,
    languageInput: string,
    languageOutput: string,
    audioFormatPayload: AudioFormatPayload,
    translateOutputType: string,
    forwardAudio: boolean = false,
  ) {
    let trackAudioStream: MediaStreamTrack | null = null;
    let streamAudioStream: MediaStream | null = null;
    const audioSource = new RTCAudioSource(audioFormatPayload.samplesPerSecond);

    try {
      this.loggerService.info('peerConnectionOnTrack', {
        message: 'Server: Received track from client',
        socketId: client.id,
        languageOutput,
        languageInput,
        audioFormatPayload,
      });

      for (const stream of event.streams) {
        this.loggerService.info('audioTracks', {
          socketId: client.id,
          audioTracksLength: stream.getAudioTracks().length,
        });

        for (const track of stream.getAudioTracks()) {
          if (track.kind !== 'audio') {
            this.loggerService.info('track.kind.not.audio', {
              socketId: client.id,
              message: `- Track kind: ${track.kind}, track id: ${track.id}`,
            });

            continue;
          }

          if (this.audioSources.has(client.id)) {
            return;
          }

          this.loggerService.info('track.kind.audio', {
            socketId: client.id,
            message: `- Track kind: ${track.kind}, track id: ${track.id}`,
          });

          this.audioSources.set(client.id, audioSource);
          trackAudioStream = track;
          streamAudioStream = stream;
        }
      }

      if (!trackAudioStream || !streamAudioStream) {
        return;
      }

      const audioTrack = audioSource.createTrack();
      peerConnection.addTrack(audioTrack, streamAudioStream);

      const audioFormat = AudioStreamFormat.getWaveFormat(
        48000,
        16,
        1,
        AudioFormatTag.PCM,
      );

      const audioSink = new nonstandard.RTCAudioSink(trackAudioStream);
      const passThrough = new PassThrough();

      const pushStream = sdk.AudioInputStream.createPushStream(audioFormat);

      if (!forwardAudio) {
        this.speechService
          .pushAudioStream(
            pushStream,
            passThrough,
            peerConnection,
            client.id,
            (speechSynthesisResult, translate, transcript, langTranscript) => {
              this.loggerService.info('pushAudioStream', {
                translate,
                transcript,
              });

              switch (translateOutputType) {
                case 'AUDIO_TRANSLATE': {
                  if (speechSynthesisResult) {
                    this.bufferQueueService.addSynthesizingAudio(
                      client.id,
                      speechSynthesisResult,
                      audioSource,
                      audioFormatPayload,
                    );
                  }

                  if (transcript) {
                    client.emit('translate', {
                      text: transcript,
                      languageInput,
                      languageOutput,
                    });
                  }
                  break;
                }
                case 'TRANSLATE': {
                  if (translate) {
                    client.emit('translate', {
                      text: translate,
                      languageInput,
                      languageOutput,
                    });
                  }
                  break;
                }
              }

              if (transcript) {
                client.emit('transcript', {
                  text: transcript,
                  languageInput,
                  languageOutput: langTranscript,
                });
              }
            },
            languageInput,
            languageOutput,
            translateOutputType,
          )
          .catch((e) => {
            this.loggerService.error('pushAudioStream', {
              stack: e.stack,
              error: { ...e },
            });
          });
      }

      this.loggerService.info('stream', {
        socketId: client.id,
        streamLength: event.streams.length,
      });

      const readyStateInterval = setInterval(() => {
        if (trackAudioStream.readyState === 'ended' || audioSink.stopped) {
          trackAudioStream.dispatchEvent(new Event('ended'));
          clearInterval(readyStateInterval);
        }
      }, 1000);

      let ffmpegProcess = null;

        console.log('forwardAudio', forwardAudio);

        // Tạo một tiến trình FFmpeg để chuyển đổi âm thanh thành PCM 48000 Hz
        ffmpegProcess = spawn('ffmpeg', [
          '-f',
          's16le', // Định dạng đầu vào: PCM signed 16-bit little-endian
          '-ar',
          '48000', // Tần số mẫu: 48000 Hz
          '-ac',
          '1', // Số kênh: mono
          '-i',
          'pipe:0', // Đầu vào từ stdin
          '-f',
          forwardAudio ? 'f32le' : 's16le', // Định dạng đầu ra: PCM signed 16-bit little-endian
          '-ar',
          forwardAudio
            ? audioFormatPayload.samplesPerSecond.toString()
            : '16000', // Đảm bảo tần số mẫu cho đầu ra là 48000 Hz
          '-ac',
          '1', // Số kênh đầu ra là mono
          'pipe:1', // Đầu ra ghi vào stdout để lấy dưới dạng buffer
        ]);

        // Lắng nghe dữ liệu từ stdout của FFmpeg
        ffmpegProcess.stdout.on('data', (data) => {
          if (
            trackAudioStream.readyState === 'live' &&
            ffmpegProcess.stdin.writable
          ) {
            if (forwardAudio) {
              client.emit('forward_audio', data);
            } else {
              pushStream.write(data);
              passThrough.write(data);
            }
          }
        });

        // Lắng nghe dữ liệu lỗi từ FFmpeg (stderr)
        ffmpegProcess.stderr.on('data', (data) => {
          const errorMessage = data.toString();
          if (
            errorMessage.includes('Error') ||
            errorMessage.includes('error')
          ) {
            this.loggerService.error('FFmpeg error:', {
              socketId: client.id,
              error: errorMessage,
            });
          }
        });

        // Xử lý khi tiến trình FFmpeg kết thúc
        ffmpegProcess.on('close', (code) => {
          this.loggerService.info('ffmpegProcessOnClose', {
            message: `FFmpeg process exited with code ${code}`,
            socketId: client.id,
          });
        });

        let sampleRateCurrent = 0;

        audioSink.ondata = (data: RTCAudioDataNonstandard) => {
          if (
            trackAudioStream.readyState === 'live' &&
            ffmpegProcess.stdin.writable
          ) {
            if (data.sampleRate !== sampleRateCurrent) {
              this.loggerService.info('audioSink data', {
                socketId: client.id,
                sampleRate: data.sampleRate,
                bitsPerSample: data.bitsPerSample,
                channelCount: data.channelCount,
                numberOfFrames: data.numberOfFrames,
              });
              sampleRateCurrent = data.sampleRate;
            }

            if (data.sampleRate === 48000 && data.channelCount === 1) {
              const buffer = Buffer.from(data.samples.buffer);
              ffmpegProcess.stdin.write(buffer);
            }
          }
        };

      trackAudioStream.onended = () => {
        if (!audioSink.stopped) {
          audioSink.stop();
          audioSink.dispatchEvent(new Event('onended'));
        }

        pushStream.close();

        if (ffmpegProcess) {
          ffmpegProcess.stdin.end();
        }
      };
    } catch (e) {
      this.loggerService.error('peerConnectionOnTrack', {
        stack: e.stack,
        socketId: client.id,
      });
    }
  }

  @SubscribeMessage('ice-candidate')
  async handleIceCandidate(
    client: Socket,
    payload: { candidate: RTCIceCandidateInit },
  ) {
    this.loggerService.info(`ce-candidate`, {
      ...payload,
      socketId: client.id,
    });

    const peerConnection = this.peerConnections.get(client.id);
    if (!peerConnection) {
      this.loggerService.info(
        `candidate Server: No peerConnection found for this client ${client.id}`,
        { ...payload, socketId: client.id },
      );
      return;
    }

    if (
      !payload?.candidate?.sdpMid ||
      payload.candidate.sdpMid.startsWith('video')
    ) {
      this.loggerService.info(
        `candidate Server: Not an audio candidate ${client.id}`,
        { ...payload, socketId: client.id },
      );
      return;
    }

    try {
      await peerConnection.addIceCandidate(
        new RTCIceCandidate(payload.candidate),
      );
      this.loggerService.info('candidateAdd', {
        message: 'Server: ICE candidate added successfully',
        socketId: client.id,
        candidate: payload.candidate,
      });
    } catch (error) {
      this.loggerService.error('ice-candidate', {
        message: `Server: Failed to add ICE candidate ${error.message}`,
        stack: error.stack,
        socketId: client.id,
      });
    }
  }

  private stopPeerConnection(peerConnection: RTCPeerConnection) {
    try {
      peerConnection.getSenders().forEach((sender) => {
        if (sender.track) {
          sender.track.stop();
        }
      });
      peerConnection.close();
    } catch (e) {
      this.loggerService.error('stopPeerConnection', {
        stack: e.stack,
        peerConnection: { ...peerConnection },
      });
    }
  }
}
