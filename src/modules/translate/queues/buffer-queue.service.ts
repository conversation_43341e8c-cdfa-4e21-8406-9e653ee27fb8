import { Injectable } from '@nestjs/common';
import { RTCAudioData, RTCAudioSource } from '@roamhq/wrtc/types/nonstandard';
import { Mutex } from 'async-mutex';
import { SpeechSynthesisResult } from 'microsoft-cognitiveservices-speech-sdk';
import { spawn } from 'node:child_process';
import { OnEvent } from '@nestjs/event-emitter';
import { AudioFormatPayload } from '../audio.interface';

enum SynthesizingAudioQueuesStatus {
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
  STARTED = 'started',
  PROCESSING = 'processing',
}

@Injectable()
export class BufferQueueService {
  private synthesizingAudioQueues: Map<string, string[]> = new Map();
  private synthesizingAudioProcessingKeys: Map<
    string,
    SynthesizingAudioQueuesStatus
  > = new Map();
  private audioProcessingKeys: Set<string> = new Set();
  private synthesizingAudioDisconnectingKeys: Set<string> = new Set();
  private synthesizingAudios: Map<string, RTCAudioData[]> = new Map();
  private audioSources: Map<string, RTCAudioSource> = new Map();
  private mutex = new Mutex();

  /**
   * Thêm buffer vào queue của một key
   */
  async addSynthesizingAudio(
    key: string,
    speechSynthesisResult: SpeechSynthesisResult,
    audioSource: RTCAudioSource,
    audioFormatPayload: AudioFormatPayload,
  ): Promise<void> {
    await this.mutex.runExclusive(async () => {
      if (this.synthesizingAudioDisconnectingKeys.has(key)) {
        return;
      }

      if (!this.synthesizingAudioQueues.has(key)) {
        this.audioSources.set(key, audioSource);
        this.synthesizingAudioQueues.set(key, []);
      }
      console.log(
        `[${speechSynthesisResult.resultId}]`,
        `Add speechSynthesisResult`,
      );

      this.synthesizingAudioProcessingKeys.set(
        speechSynthesisResult.resultId,
        SynthesizingAudioQueuesStatus.STARTED,
      );
      this.synthesizingAudioQueues
        .get(key)
        .push(speechSynthesisResult.resultId);
      this.convertSpeechSynthesisToBuffer(
        key,
        speechSynthesisResult,
        audioFormatPayload,
      );
    });
  }

  convertSpeechSynthesisToBuffer(
    key: string,
    speechSynthesisResult: SpeechSynthesisResult,
    audioFormatPayload: AudioFormatPayload,
  ) {
    console.log(
      `[${speechSynthesisResult.resultId}]`,
      `convertSpeechSynthesisToBuffer buffer`,
    );

    this.synthesizingAudioProcessingKeys.set(
      speechSynthesisResult.resultId,
      SynthesizingAudioQueuesStatus.PROCESSING,
    );

    const audioDataBuffer = Buffer.from(speechSynthesisResult.audioData);

    const ffmpegProcess = spawn('ffmpeg', [
      '-i',
      'pipe:0', // Đầu vào từ stdin
      '-f',
      's16le', // Định dạng đầu ra: PCM signed 16-bit little-endian
      '-ar',
      audioFormatPayload.samplesPerSecond.toString(), // Tần số mẫu cho đầu ra
      '-ac',
      '1', // Số kênh đầu ra là mono
      'pipe:1', // Đầu ra ghi vào stdout để lấy dưới dạng buffer
    ]);

    const audioDataTransformed: Buffer[] = [];
    ffmpegProcess.stdout.on('data', (data) => {
      audioDataTransformed.push(data);
    });

    // Xử lý khi tiến trình FFmpeg kết thúc
    ffmpegProcess.on('close', async (code) => {
      console.log(`[${speechSynthesisResult.resultId}]`, 'FFmpeg close');
      const concatenatedBuffer = Buffer.concat(audioDataTransformed);
      const audioData = this.convertBufferToRTCAudioData(
        concatenatedBuffer,
        audioFormatPayload.samplesPerSecond,
      );
      if (audioData.length === 0) {
        console.log('No audio data');
        return;
      }

      this.synthesizingAudios.set(speechSynthesisResult.resultId, audioData);
      console.log(
        `[${speechSynthesisResult.resultId}]`,
        'Set synthesizingAudios',
      );

      if (this.synthesizingAudioDisconnectingKeys.has(key) || code !== 0) {
        this.synthesizingAudioProcessingKeys.set(
          speechSynthesisResult.resultId,
          SynthesizingAudioQueuesStatus.FAILED,
        );
        return;
      }

      this.synthesizingAudioProcessingKeys.set(
        speechSynthesisResult.resultId,
        SynthesizingAudioQueuesStatus.SUCCEEDED,
      );

      // Nếu key chưa được xử lý, bắt đầu xử lý
      if (!this.audioProcessingKeys.has(key)) {
        await this.processNextBuffer(key);
      }
    });

    ffmpegProcess.stdin.write(audioDataBuffer);
    ffmpegProcess.stdin.end();
  }

  convertBufferToRTCAudioData(
    buffer: Buffer,
    sampleRate = 8000,
  ): RTCAudioData[] {
    const numberOfSamplesPerFrame = sampleRate / 100;
    const leftoverSamples = new Int16Array(numberOfSamplesPerFrame);
    let numLeftoverSamples = 0;
    let audioData = [];

    const samples: Int16Array = new Int16Array(
      buffer.buffer,
      buffer.byteOffset,
      buffer.byteLength / Int16Array.BYTES_PER_ELEMENT,
    );

    let chunkStart = 0;

    while (chunkStart < samples.length) {
      const wantedNumberOfSamples =
        numberOfSamplesPerFrame - numLeftoverSamples;

      const remainingSamples = samples.length - chunkStart;

      if (remainingSamples < wantedNumberOfSamples) {
        leftoverSamples.set(samples.slice(chunkStart));
        numLeftoverSamples = remainingSamples;

        return audioData;
      }

      let chunk = samples.slice(chunkStart, chunkStart + wantedNumberOfSamples);

      if (numLeftoverSamples) {
        leftoverSamples.set(chunk, numLeftoverSamples);

        chunk = leftoverSamples;

        numLeftoverSamples = 0;
      }

      audioData.push({
        samples: chunk,
        numberOfFrames: numberOfSamplesPerFrame,
        sampleRate: sampleRate,
      });

      chunkStart += wantedNumberOfSamples;
    }

    return audioData;
  }

  /**
   * Xử lý buffer tiếp theo trong hàng đợi của key
   */
  private async processNextBuffer(key: string): Promise<void> {
    if (this.synthesizingAudioDisconnectingKeys.has(key)) {
      return;
    }

    const queue = this.synthesizingAudioQueues.get(key);
    if (!queue || queue.length === 0) {
      this.audioProcessingKeys.delete(key);
      return;
    }

    // Đánh dấu key đang được xử lý
    this.audioProcessingKeys.add(key);

    try {
      // Lấy buffer đầu tiên trong hàng đợi
      const audioDataId = await this.mutex.runExclusive(() => queue.shift());
      if (audioDataId) {
        await this.processBuffer(key, audioDataId);
      }
    } catch (error) {
      console.error(`Error processing buffer for key: ${key}`, error);
    } finally {
      // Tiếp tục xử lý buffer tiếp theo
      await this.processNextBuffer(key);
    }
  }

  /**
   * Logic xử lý buffer
   */
  private async processBuffer(key: string, audioDataId: string): Promise<void> {
    console.log(`[${audioDataId}]`, `Processing buffer`);
    const audioSource = this.audioSources.get(key);
    if (!audioSource) return;

    let synthesizingAudioStatus =
      this.synthesizingAudioProcessingKeys.get(audioDataId);

    if (
      synthesizingAudioStatus === SynthesizingAudioQueuesStatus.FAILED ||
      this.synthesizingAudioDisconnectingKeys.has(key)
    ) {
      console.error(`[${audioDataId}]`, 'Process Buffer Stop');
      this.synthesizingAudios.delete(audioDataId);
      this.synthesizingAudioProcessingKeys.delete(audioDataId);
      return;
    }

    let tries = 0;

    // Nếu buffer chưa được xử lý thì đợi
    while (
      synthesizingAudioStatus === SynthesizingAudioQueuesStatus.PROCESSING
    ) {
      if (tries > 300) {
        console.error(`[${audioDataId}]`, 'Buffer not found');
        break;
      }

      console.log(`[${audioDataId}]`, 'Pending get buffer');
      await new Promise((r) => setTimeout(r, 100));
      synthesizingAudioStatus =
        this.synthesizingAudioProcessingKeys.get(audioDataId);

      tries++;
    }

    const audioData = this.synthesizingAudios.get(audioDataId) || [];

    try {
      for (const audio of audioData) {
        audioSource.onData(audio);
        await new Promise((r) => setTimeout(r, 8));
      }
      console.log(`[${audioDataId}]`, 'Finished processing buffer');
    } catch (error) {
      console.error(`[${audioDataId}]`, audioSource);
      console.error(`[${audioDataId}]`, error);
    } finally {
      this.synthesizingAudios.delete(audioDataId);
      this.synthesizingAudioProcessingKeys.delete(audioDataId);
    }
  }

  @OnEvent('PeerDisconnect', { async: true })
  async onSynthesizeAudio(payload: { socketId: string }) {
    this.deleteQueue(payload.socketId);
  }

  private deleteQueue(key: string) {
    this.synthesizingAudioDisconnectingKeys.add(key);
    const audioDataIds = this.synthesizingAudioQueues.get(key) || [];
    for (const audioDataId of audioDataIds) {
      this.synthesizingAudios.delete(audioDataId);
      this.synthesizingAudioProcessingKeys.delete(audioDataId);
    }

    this.synthesizingAudioQueues.delete(key);
    this.audioProcessingKeys.delete(key);
    this.synthesizingAudioDisconnectingKeys.delete(key);
  }
}
