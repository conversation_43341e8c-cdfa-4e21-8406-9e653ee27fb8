import { Controller, Get } from '@nestjs/common';
import { TranslateService } from '../translate/translate.service';
import { CallManagerService } from './call-manager.service';

@Controller('call-manager')
export class CallManagerController {
  constructor(private callManagerService: CallManagerService) {}

  @Get('calls')
  getCallHistories(): Promise<RTCIceServer[]> {
    return this.callManagerService.getIceServers();
  }
}
