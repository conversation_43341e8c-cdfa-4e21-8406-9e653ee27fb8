import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AZURE_SERVICE, AzureConfig } from './schemas/azure-config.schema';
import { Model } from 'mongoose';
import _ from 'lodash';
@Injectable()
export class TranslateService {
  constructor(
    @InjectModel(AzureConfig.name)
    private configModel: Model<AzureConfig>,
  ) {}

  async translate(text: string, to: string): Promise<string> {
    const config = await this.configModel.findOne({
      service: AZURE_SERVICE.TRANSLATE_SERVICE,
    });
    const region = config.region;
    const endpoint = config.endpoint;
    const key = config.key;

    const headers = {
      'Ocp-Apim-Subscription-Key': key,
      'Ocp-Apim-Subscription-Region': region,
      'Content-Type': 'application/json',
    };
    const curl = await fetch(`${endpoint}/translate?api-version=3.0&to=${to}`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify([
        {
          Text: text,
        },
      ]),
    });
    const response = await curl.json();
    return _.get(response, '0.translations.0.text');
  }
}
