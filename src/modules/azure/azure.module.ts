import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AzureConfig, AzureConfigSchema } from './schemas/azure-config.schema';
import { AzureGateway } from './azure.gateway';
import { SpeechService } from './speech.service';
import { TranslateService } from './translate.service';
import {
  LanguageSupport,
  LanguageSupportSchema,
} from './schemas/language-support.schema';
import { RedisModule } from '../../share_modules/redis/redis.module';
import { RedisService } from '../../share_modules/redis/redis.service';
import {
  TranslateProcess,
  TranslateProcessSchema,
} from './schemas/translate-process.schema';
import { LoggerModule } from 'src/share_modules/logging/logger.module';
import { LogAzure, LogAzureSchema } from './schemas/log-azure.schema';
import {
  TextToSpeechHistoryModel,
  TextToSpeechHistorySchema,
} from './schemas/text-to-speech-history.model';
import {
  TranslateTextHistoryModel,
  TranslateTextHistorySchema,
} from './schemas/translate-text-history.model';
import { GoogleCloudStorageModule } from '../../share_modules/google-cloud-storage/google-cloud-storage.module';
import {
  AZureReplace,
  AZureReplaceSchema,
} from './schemas/azure-replace.schema';
import { ReplaceWordService } from './replace-word.service';
import { AudioTranscriptionModule } from '../../share_modules/audio-transcription/audio-transcription.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: AzureConfig.name,
        schema: AzureConfigSchema,
      },
      {
        name: LanguageSupport.name,
        schema: LanguageSupportSchema,
      },
      {
        name: TranslateProcess.name,
        schema: TranslateProcessSchema,
      },
      {
        name: LogAzure.name,
        schema: LogAzureSchema,
      },
      {
        name: TextToSpeechHistoryModel.name,
        schema: TextToSpeechHistorySchema,
      },
      {
        name: TranslateTextHistoryModel.name,
        schema: TranslateTextHistorySchema,
      },
      {
        name: AZureReplace.name,
        schema: AZureReplaceSchema,
      },
    ]),
    RedisModule,
    LoggerModule,
    GoogleCloudStorageModule,
    AudioTranscriptionModule,
  ],
  controllers: [],
  providers: [
    AzureGateway,
    SpeechService,
    TranslateService,
    RedisService,
    ReplaceWordService,
  ],
  exports: [SpeechService, MongooseModule],
})
export class AzureModule {}
