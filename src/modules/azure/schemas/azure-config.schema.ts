import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export enum AZURE_SERVICE {
  SPEECH_SERVICE = 'SPEECH_SERVICE',
  TRANSLATE_SERVICE = 'TRANSLATE_SERVICE',
}

@Schema({
  collection: 'azure_configs',
})
export class AzureConfig {
  @Prop()
  service: string;

  @Prop()
  region: string;

  @Prop()
  key: string;

  @Prop()
  endpoint: string;

  @Prop()
  languages: string[];

  @Prop()
  bad_words: string[];
}
export const AzureConfigSchema = SchemaFactory.createForClass(AzureConfig);
