import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({
  collection: 'speech_to_text_histories',
  timestamps: true,
})
export class SpeechToTextHistoryModel {
  @Prop({ type: String, required: true, index: true })
  socket_id: string;

  @Prop({ type: String, required: true })
  text: string;
}

export const SpeechToTextHistorySchema = SchemaFactory.createForClass(
  SpeechToTextHistoryModel,
);
