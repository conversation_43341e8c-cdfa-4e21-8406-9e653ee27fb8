import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({
  collection: 'translate_text_histories',
  timestamps: true,
})
export class TranslateTextHistoryModel {
  @Prop({ type: String, required: true, index: true })
  socket_id: string;

  @Prop({ type: String, required: true })
  text: string;

  @Prop({ type: String, required: true })
  translate: string;

  @Prop({ type: String, required: true })
  language: string;

  @Prop({ type: Object, required: true })
  split: object;

  @Prop({ type: Number, required: true })
  count: number;
}

export const TranslateTextHistorySchema = SchemaFactory.createForClass(
  TranslateTextHistoryModel,
);
