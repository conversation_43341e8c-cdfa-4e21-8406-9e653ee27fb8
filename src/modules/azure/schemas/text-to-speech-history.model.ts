import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({
  collection: 'text_to_speech_histories',
  timestamps: true,
})
export class TextToSpeechHistoryModel {
  @Prop({ type: String, required: true, index: true })
  socket_id: string;

  @Prop({ type: String, required: true })
  text: string;

  @Prop({ type: String, required: true })
  language: string;

  @Prop({ type: Number, required: true })
  count: number;
}

export const TextToSpeechHistorySchema = SchemaFactory.createForClass(
  TextToSpeechHistoryModel,
);
