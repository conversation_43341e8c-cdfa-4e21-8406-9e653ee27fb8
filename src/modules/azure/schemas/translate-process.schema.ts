import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
export enum TRANSLATE_PROCESS_TYPE {
  INPUT = 'INPUT',
  AUDIO = 'AUDIO',
  TRANSLATE = 'TRANSLATE',
}

export enum AZURE_PROCESS_STATUS {
  SAVE_AUDIO = 'SAVE_AUDIO',
  SENT = 'SENT',
  PROCESSING = 'PROCESSING',
}
@Schema({
  collection: 'azure_process',
  timestamps: true,
})
export class TranslateProcess {
  @Prop()
  session_id: string;

  @Prop()
  no: number;

  @Prop()
  text: string;

  @Prop()
  text_translate: string;

  @Prop()
  uuid: string;

  @Prop()
  status: string;

  @Prop()
  type: string;

  @Prop()
  no_translate: number;

  @Prop({ type: String })
  audio_id: string;

  @Prop({ type: String })
  audio_url: string;

  @Prop({ type: Date })
  audio_play_time: Date;
}

export const TranslateProcessSchema =
  SchemaFactory.createForClass(TranslateProcess);
