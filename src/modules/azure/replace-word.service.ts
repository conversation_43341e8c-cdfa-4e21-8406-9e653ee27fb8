import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AZureReplace } from './schemas/azure-replace.schema';
import { Model } from 'mongoose';

@Injectable()
export class ReplaceWordService {
  private configs: any = [];

  constructor(
    @InjectModel(AZureReplace.name)
    private readonly replaceModel: Model<AZureReplace>,
  ) {}

  async replaceToDot(text: string): Promise<string> {
    try {
      if (this.configs.length == 0) {
        await this.getConfigs();
      }
      this.configs.forEach((item) => {
        if (item.type === 'NOT_DOT') {
          const regex = new RegExp(item.from, 'gi'); // 'gi' để không phân biệt hoa thường
          text = text.replace(regex, item.to);
        }
      });
      return text;
    } catch (e) {
      return text;
    }
  }

  async getConfigs(): Promise<void> {
    const find = await this.replaceModel.find().exec();
    this.configs = find;
  }

  convertBefore(text: string): string {
    try {
      text = this.fiveNumberBeforeYear(text);
      text = this.numberBeforeFiveNumber(text);
      text = this.replaceWithTypeReplace(text);
      return text;
    } catch (e) {
      return text;
    }
  }

  fiveNumberBeforeYear(sentence: string): string {
    sentence = sentence.replace(/(\d)[.,](?=\d)/g, (match, p1) => {
      return match.includes('.') ? `${p1}_dot_` : `${p1}_comma_`;
    });
    // 1. "5" followed by a year with spaced digits (e.g., '5 1 9 9 7')
    // 2. "5" followed by a standard year (e.g., '5 1997')
    const regex = /\b5[,]?\s{1,}((\d[\s,]*){3}\d|\d{4}|\d{2}|\d{1})\b/g;
    sentence = sentence.replace(regex, (match, yearGroup) => {
      // If the year is spaced (e.g., '1 9 9 7'), remove spaces
      const year = yearGroup.includes(' ')
        ? yearGroup.replace(/\s+/g, '')
        : yearGroup;
      // Replace "5" with "năm" followed by the reconstructed year
      return `năm ${year}`;
    });
    return sentence.replaceAll('_dot_', '.').replaceAll('_comma_', ',');
  }

  numberBeforeFiveNumber(input: string): string {
    // Use regex to match the pattern of a number followed by " 5"
    return input.replace(/(\d+) 5/g, (match, p1) => `${p1} năm`);
  }

  replaceWithTypeReplace(text: string): string {
    this.configs.forEach((item) => {
      if (item.type === 'REPLACE') {
        const regex = new RegExp(item.from, 'gi'); // 'gi' để không phân biệt hoa thường
        text = text.replace(regex, item.to);
      }
    });
    return text;
  }

  async replaceWithSameWord({ text, textOrigin }): Promise<string> {
    if (!textOrigin) {
      return text;
    }
    try {
      let textEdit = text;
      if (this.configs.length === 0) {
        await this.getConfigs();
      }
      const dataReplace = this.configs.filter((item) => {
        return item.type === 'REPLACE_SAME_WORD';
      });
      dataReplace.forEach((item: any) => {
        const regex = new RegExp(item.from, 'gi'); // 'gi' để không phân biệt hoa thường
        const replaces = item.to;
        let match;
        const matches = [];
        while ((match = regex.exec(textEdit)) !== null) {
          matches.push(match.index);
        }
        if (matches.length === 0) {
          return;
        }
        const matchedOfReplace = [];
        replaces.forEach((item) => {
          let match;
          const regex = new RegExp(item, 'gi');
          while ((match = regex.exec(textOrigin)) !== null) {
            matchedOfReplace.push({
              text: match[0],
              index: match.index,
            });
          }
        });
        matchedOfReplace.sort((a, b) => a.index - b.index);
        let c = 0;
        matches.forEach(() => {
          const regex = new RegExp(item.from, 'gi');
          textEdit = textEdit.replace(regex, () => {
            if (c < matchedOfReplace.length) {
              return matchedOfReplace[c++].text; // Replace "bà 4" with the next item in dataReplace
            }
            return item.from;
          });
        });
      });
      return textEdit;
    } catch (e) {
      return text;
    }
  }
}
