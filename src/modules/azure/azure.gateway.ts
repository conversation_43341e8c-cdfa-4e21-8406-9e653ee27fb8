import {
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
} from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { SpeechService } from './speech.service';
import {
  AudioFormatTag,
  AudioInputStream,
  AudioStreamFormat,
  PushAudioInputStream,
} from 'microsoft-cognitiveservices-speech-sdk';

@WebSocketGateway({
  cors: true,
  namespace: '/azure',
})
export class AzureGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  private audioStream: PushAudioInputStream;
  constructor(private readonly speechService: SpeechService) {}

  handleConnection(socket: Socket): void {
    const audioFormat = AudioStreamFormat.getWaveFormat(
      48000,
      16,
      1,
      AudioFormatTag.PCM,
    ); // 16 kHz, Mono, PCM
    this.audioStream = AudioInputStream.createPushStream(audioFormat);
    // this.speechService
    //   .pushAudioStream(
    //     this.audioStream,
    //     socket.id,
    //     (data, translate, transcript, lang_transcript) => {
    //       console.log({ data, translate, transcript, lang_transcript });
    //       //console.log(data.audioData.byteLength);
    //     },
    //     'vi-VN,en-US',
    //     'ko-KR',
    //     'TRANSCRIPT',
    //   )
    //   .then();
  }

  afterInit(server: any): any {
    //this.speechService.on('data',chunk => console.log(chunk.toString()))
  }

  handleDisconnect(socket: Socket): void {
    console.log(`Client disconnect: ${socket.id}`);
  }

  @SubscribeMessage('STREAM')
  async messageStream(@MessageBody() data: ArrayBuffer): Promise<void> {
    this.audioStream.write(data);
  }

  @SubscribeMessage('STOP')
  handleStop(): void {
    this.speechService.stop();
    return;
  }
}
