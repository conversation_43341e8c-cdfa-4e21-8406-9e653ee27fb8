import { Injectable } from '@nestjs/common';
import {
  AudioConfig,
  AutoDetectSourceLanguageConfig,
  AutoDetectSourceLanguageResult,
  OutputFormat,
  ProfanityOption,
  PushAudioInputStream,
  Recognizer,
  ResultReason,
  SpeechConfig,
  SpeechRecognitionEventArgs,
  SpeechRecognizer,
  SpeechSynthesizer,
} from 'microsoft-cognitiveservices-speech-sdk';
import { InjectModel } from '@nestjs/mongoose';
import { AZURE_SERVICE, AzureConfig } from './schemas/azure-config.schema';
import { Model } from 'mongoose';
import { TranslateService } from './translate.service';
import { SpeechSynthesisResult } from 'microsoft-cognitiveservices-speech-sdk/distrib/lib/src/sdk/Exports';
import { LanguageSupport } from './schemas/language-support.schema';
import { RedisService } from '../../share_modules/redis/redis.service';
import {
  AZURE_PROCESS_STATUS,
  TRANSLATE_PROCESS_TYPE,
  TranslateProcess,
} from './schemas/translate-process.schema';
import { EventEmitter } from 'events';
import { generateUUID, splitAddPunctuation } from '../../utils/common';
import { LoggerService } from 'src/share_modules/logging/logger.service';
import { LogAzure } from './schemas/log-azure.schema';
import { TextToSpeechHistoryModel } from './schemas/text-to-speech-history.model';
import { TranslateTextHistoryModel } from './schemas/translate-text-history.model';
import { GoogleCloudStorageService } from '../../share_modules/google-cloud-storage/google-cloud-storage.service';
import { ReplaceWordService } from './replace-word.service';
import { AudioStreamSocketService } from '../../share_modules/audio-transcription/audio-stream-socket.service';
import { AudioStreamDeepgramService } from '../../share_modules/audio-transcription/deepgram-audio-stream.service';
import { nonstandard } from '@roamhq/wrtc';
import { ConfigService } from '@nestjs/config';
import { PassThrough } from 'stream';
const _ = require('lodash');

@Injectable()
export class SpeechService {
  private audioStream: PushAudioInputStream;
  private recognizer: SpeechRecognizer;
  sessionIds: string[] = [];
  eventData = new EventEmitter();
  dataAudio: Map<string, SpeechSynthesisResult> = new Map();

  constructor(
    @InjectModel(AzureConfig.name)
    private configModel: Model<AzureConfig>,
    @InjectModel(LanguageSupport.name)
    private languageModel: Model<LanguageSupport>,
    @InjectModel(TranslateProcess.name)
    private readonly processModel: Model<TranslateProcess>,
    private readonly translateService: TranslateService,
    private readonly redisService: RedisService,
    private readonly loggerService: LoggerService,
    private readonly googleCloudStorageService: GoogleCloudStorageService,
    @InjectModel(LogAzure.name)
    private readonly logZureModel: Model<LogAzure>,
    @InjectModel(TextToSpeechHistoryModel.name)
    private readonly textToSpeechHistoryModel: Model<TextToSpeechHistoryModel>,
    @InjectModel(TranslateTextHistoryModel.name)
    private readonly translateTextHistoryModel: Model<TranslateTextHistoryModel>,
    private readonly replaceWordService: ReplaceWordService,
    private readonly audioStreamSocketService: AudioStreamSocketService,
    private readonly audioStreamDeepgramService: AudioStreamDeepgramService,
    
    private readonly configService: ConfigService,
  ) {
    // this.replaceWordService
    //   .replaceWithSameWord({
    //     text: 'Vào ngày 3 tháng mười hai năm 1997, bà 4 cùng ông 4 và ông 5 tổ chức một buổi họp mặt gia đình ấm cúng tại nhà bà 4.',
    //     textOrigin:
    //       'vào ngày ba tháng mười hai năm một nghìn chín trăm chín mươi bảy bà tư cùng ông bốn và ông năm tổ chức một buổi họp mặt gia đình ấm cúng tại nhà bà tư',
    //   })
    //   .then((res) => {
    //     console.log(res);
    //   });
  }

  async pushAudioStream(
    audioStream: PushAudioInputStream,
    passThrough: PassThrough,
    peerConnection: RTCPeerConnection,
    sessionId: string = null,
    callback: (
      data?: SpeechSynthesisResult,
      translate?: string,
      transcript?: string,
      lang_transcript?: string,
    ) => void,
    localeInput: string = 'vi-VN',
    locale: string = 'en-US',
    typeOutput?: string,
  ): Promise<void> {
    this.loggerService.info(`====>input,${localeInput}`, { localeInput });
    if (this.sessionIds.indexOf(sessionId) !== -1) {
      return;
    }
    await this.replaceWordService.getConfigs();

    this.eventData.on(
      sessionId,
      async (
        result?: SpeechSynthesisResult,
        translate?: string,
        transcript?: string,
        langTranscript?: string,
      ) => {
        callback(result, translate, transcript, langTranscript);
        if (!result) {
          return;
        }
        try {
          await this.processModel.updateOne(
            {
              audio_id: result.resultId,
            },
            {
              audio_play_time: new Date(),
            },
          );

          const buffer = Buffer.from(result.audioData);
          const audioUrl = await this.googleCloudStorageService.uploadBuffer(
            buffer,
            `${sessionId}_${result.resultId}_audio.wav`,
          );

          await this.processModel.updateOne(
            {
              audio_id: result.resultId,
            },
            {
              audio_url: audioUrl,
            },
          );
        } catch (error) {
          this.loggerService.error('Save audio error', {
            socketId: sessionId,
            error: error.message,
            audioId: result.resultId,
          });
          console.log(error);
        }
      },
    );
    let count = 0;
    const language = await this.redisLanguage(sessionId, locale);
    this.loggerService.info('pushAudioStream locale', {
      locale,
      language,
    });
    const config = await this.config();
    const badWords = config.bad_words || [];

    if (typeOutput === 'TRANSCRIPT' && localeInput.split(',').length === 1) {
      this.audioStreamDeepgramService.startStreaming(peerConnection, passThrough, localeInput.split('-')[0]);

      // const socketUrl = this.configService.get<string>('STT_REALTIME_WS_URL') + '/asr';
      // const streamControl = this.audioStreamSocketService.startStreaming(
      //   sessionId,
      //   audioSink,
      //   {
      //     socketUrl: socketUrl,
      //     language: localeInput.split('-')[0],
      //   },
      //   async (data) => {
      //     const text = data.new_transcription;
      //     if (!text) return;
      //     console.log(text);
      //     const textOrigin = text;

      //     await this.textToSpeechHistoryModel.create({
      //       socket_id: sessionId,
      //       text: text,
      //       language: localeInput,
      //       count: count,
      //     });
      //     await this.logZureModel
      //       .create({
      //         type: 'recognized',
      //         data: {
      //           sessionId,
      //           localeInput,
      //           locale,
      //           text,
      //           textOrigin,
      //           in_badWords: badWords.indexOf(text) !== -1,
      //         },
      //       })
      //       .then();
      //     if (badWords.indexOf(text) !== -1) {
      //       return;
      //     }
      //     let textConverted = this.replaceWordService.convertBefore(text);
      //     textConverted = await this.replaceWordService.replaceWithSameWord({
      //       text: textConverted,
      //       textOrigin,
      //     });

      //     this.eventData.emit(
      //       sessionId,
      //       null,
      //       null,
      //       textConverted,
      //       localeInput,
      //     );
      //   },
      // );

      // audioSink.addEventListener('onended', () => {
      //   streamControl.stop();
      // });

      return;
    }

    // if (typeOutput === 'TRANSCRIPT' && localeInput.split(',').length === 1) {
    //   const socketUrl =
    //     this.configService.get<string>('STT_REALTIME_WS_URL') + '/asr';
    //   const streamControl = this.audioStreamSocketService.startStreaming(
    //     sessionId,
    //     audioSink,
    //     {
    //       socketUrl: socketUrl,
    //       language: localeInput.split('-')[0],
    //     },
    //     async (data) => {
    //       const text = data.new_transcription;
    //       if (!text) return;
    //       console.log(text);
    //       const textOrigin = text;

    //       await this.textToSpeechHistoryModel.create({
    //         socket_id: sessionId,
    //         text: text,
    //         language: localeInput,
    //         count: count,
    //       });
    //       await this.logZureModel
    //         .create({
    //           type: 'recognized',
    //           data: {
    //             sessionId,
    //             localeInput,
    //             locale,
    //             text,
    //             textOrigin,
    //             in_badWords: badWords.indexOf(text) !== -1,
    //           },
    //         })
    //         .then();
    //       if (badWords.indexOf(text) !== -1) {
    //         return;
    //       }
    //       let textConverted = this.replaceWordService.convertBefore(text);
    //       textConverted = await this.replaceWordService.replaceWithSameWord({
    //         text: textConverted,
    //         textOrigin,
    //       });

    //       this.eventData.emit(
    //         sessionId,
    //         null,
    //         null,
    //         textConverted,
    //         localeInput,
    //       );
    //     },
    //   );

    //   audioSink.addEventListener('onended', () => {
    //     streamControl.stop();
    //   });

    //   return;
    // }

    // if (typeOutput === 'TRANSCRIPT') {
    //   this.audioTranscriptionService.startProcessing(
    //     sessionId,
    //     audioSink,
    //     async (text: string, segments: [], language: string) => {
    //       if (!text) return;
    //
    //       const textOrigin = text;
    //
    //       await this.textToSpeechHistoryModel.create({
    //         socket_id: sessionId,
    //         text: text,
    //         language: localeInput,
    //         count: count,
    //       });
    //       await this.logZureModel
    //         .create({
    //           type: 'recognized',
    //           data: {
    //             sessionId,
    //             localeInput,
    //             locale,
    //             text,
    //             textOrigin,
    //             in_badWords: badWords.indexOf(text) !== -1,
    //           },
    //         })
    //         .then();
    //       if (badWords.indexOf(text) !== -1) {
    //         return;
    //       }
    //       let textConverted = this.replaceWordService.convertBefore(text);
    //       textConverted = await this.replaceWordService.replaceWithSameWord({
    //         text: textConverted,
    //         textOrigin,
    //       });
    //
    //       this.eventData.emit(
    //         sessionId,
    //         null,
    //         null,
    //         textConverted,
    //         localeInput,
    //       );
    //     },
    //     localeInput.split('-')[0],
    //   );
    //
    //   return;
    // }

    const speechConfig = SpeechConfig.fromSubscription(
      config.key,
      config.region,
    );
    console.log([...localeInput.split(',')]);
    if (localeInput.split(',').length > 1) {
      //speechConfig.autoDetectSourceLanguages = "en-US,vi-VN"
    } else {
      speechConfig.speechRecognitionLanguage = localeInput;
    }
    speechConfig.setProperty(
      'SpeechServiceConnection_OutputFormat',
      'Detailed',
    );
    speechConfig.outputFormat = OutputFormat.Detailed;
    speechConfig.setProfanity(ProfanityOption.Raw);
    let recognizer: SpeechRecognizer;
    const audioConfig = AudioConfig.fromStreamInput(audioStream);
    if (localeInput.split(',').length === 1) {
      recognizer = new SpeechRecognizer(speechConfig, audioConfig);
    } else {
      const autoDetectConfig = AutoDetectSourceLanguageConfig.fromLanguages([
        ...localeInput.split(','),
      ]);
      speechConfig.setProperty(
        'SpeechServiceConnection_LanguageIdMode',
        'Continuous',
      );
      recognizer = SpeechRecognizer.FromConfig(
        speechConfig,
        autoDetectConfig,
        audioConfig,
      );
    }
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    recognizer.recognizing = (s: Recognizer, e: SpeechRecognitionEventArgs) => {
      // this.processInQueue(sessionId);
    };
    recognizer.recognized = async (
      s: Recognizer,
      e: SpeechRecognitionEventArgs,
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    ) => {
      if (!e.result.text) return;
      count++;
      const text = e.result.text;
      //JSON.parse(e.result.json)['NBest'][0]['Lexical']
      let textOrigin = '';
      try {
        textOrigin = _.get(JSON.parse(e.result.json), 'NBest.0.Lexical', '');
      } catch (e) {}

      await this.textToSpeechHistoryModel.create({
        socket_id: sessionId,
        text: text,
        language: localeInput,
        count: count,
      });
      await this.logZureModel
        .create({
          type: 'recognized',
          data: {
            sessionId,
            localeInput,
            locale,
            text,
            textOrigin,
            in_badWords: badWords.indexOf(text) !== -1,
          },
        })
        .then();
      if (badWords.indexOf(text) !== -1) {
        return;
      }
      let textConverted = this.replaceWordService.convertBefore(text);
      textConverted = await this.replaceWordService.replaceWithSameWord({
        text: textConverted,
        textOrigin,
      });
      let langTranscript = localeInput;
      try {
        const autoDetectSourceLanguageResult =
          AutoDetectSourceLanguageResult.fromResult(e.result);
        langTranscript = autoDetectSourceLanguageResult.language;
        console.log('langTranscript', autoDetectSourceLanguageResult);
      } catch (e) {
        console.log(e.message);
      }
      if (typeOutput === 'TRANSCRIPT') {
        this.eventData.emit(
          sessionId,
          null,
          null,
          textConverted,
          langTranscript,
        );
        return;
      }
      this.addToQueue({
        session_id: sessionId,
        language: language,
        text: textConverted,
        count: count,
        typeOutput: typeOutput,
      }).then(() => {
        this.logZureModel.create({
          type: 'converted',
          data: { text, textConverted },
        });
      });
    };
    recognizer.canceled = (s, e) => {
      this.logZureModel
        .create({
          type: 'canceled',
          data: JSON.stringify({ sessionId, error: e.errorDetails || '' }),
        })
        .then();
      console.trace(e.errorDetails);
    };
    recognizer.startContinuousRecognitionAsync(() => {
      this.logZureModel
        .create({
          type: 'started',
          data: { sessionId, localeInput },
        })
        .then();
      console.log('start success');
    });
  }

  async config(): Promise<AzureConfig> {
    return await this.configModel
      .findOne({
        service: AZURE_SERVICE.SPEECH_SERVICE,
      })
      .exec();
  }

  stop(): void {
    this.audioStream.close();
    this.recognizer.stopContinuousRecognitionAsync();
  }

  async textToSpeech(
    text: string,
    to: string,
    voice: string,
  ): Promise<SpeechSynthesisResult> {
    const config = await this.configModel
      .findOne({
        service: AZURE_SERVICE.SPEECH_SERVICE,
      })
      .exec();
    const speechConfig = SpeechConfig.fromSubscription(
      config.key,
      config.region,
    );
    const audioConfig = AudioConfig.fromDefaultSpeakerOutput();

    speechConfig.speechRecognitionLanguage = to;
    speechConfig.speechSynthesisVoiceName = voice;
    const synthesizer = new SpeechSynthesizer(speechConfig, audioConfig);
    return new Promise((resolve: any, reject: any) => {
      synthesizer.speakTextAsync(
        text,
        function (result) {
          if (result.reason === ResultReason.SynthesizingAudioCompleted) {
            resolve(result);
          }
          synthesizer.close();
        },
        function (err) {
          console.trace('err - ' + err);
          synthesizer.close();
          reject('err - ' + err);
        },
      );
    });
  }

  async getLanguage(locale: string = null): Promise<LanguageSupport[]> {
    let cond = {};
    if (locale) {
      cond = {
        locale: locale,
      };
    }
    return await this.languageModel.find(cond).exec();
  }

  async redisLanguage(
    sessionId: string,
    locale: string,
  ): Promise<LanguageSupport | null> {
    const key = `translate:${sessionId}:language`;
    const data = await this.redisService.get(key);
    if (data) {
      return JSON.parse(data);
    }
    const findData = await this.getLanguage(locale);
    if (findData.length === 0) {
      return null;
    }
    await this.redisService.set(key, JSON.stringify(findData[0]));
    return findData[0];
  }

  async addToQueue({
    session_id,
    language,
    text,
    count,
    typeOutput,
  }: any): Promise<void> {
    const translateProcess = await this.processModel.create({
      session_id: session_id,
      no: count,
      text: text,
      type: TRANSLATE_PROCESS_TYPE.INPUT,
    });
    const translate = await this.translateService.translate(
      text,
      language.code,
    );
    await this.processModel.updateOne(
      {
        _id: translateProcess._id,
      },
      {
        $set: {
          text_translate: translate,
        },
      },
    );
    const textNew = await this.replaceWordService.replaceToDot(translate);
    const sp = splitAddPunctuation(textNew);
    this.logZureModel
      .create({
        type: 'translate',
        data: {
          sessionId: session_id,
          text,
          translate,
          before_sp: textNew,
          sp: sp,
        },
      })
      .then();
    const dataSave = sp.map((item: any, index: number) => {
      const textTranslate = `${item.text}${item.punctuation}`;
      return {
        session_id: session_id,
        no: count,
        text: text,
        type: TRANSLATE_PROCESS_TYPE.TRANSLATE,
        text_translate: textTranslate,
        uuid: generateUUID(),
        no_translate: index,
      };
    });

    await Promise.all([
      this.translateTextHistoryModel.create({
        socket_id: session_id,
        text: text,
        translate: translate,
        language: language.code,
        split: dataSave,
        count: count,
      }),
      this.processModel.insertMany(dataSave),
    ]);

    dataSave.forEach((item) => {
      this.textToSpeech(
        item.text_translate,
        language.locale,
        language.voice,
      ).then(async (data: SpeechSynthesisResult) => {
        this.dataAudio.set(item.uuid, data);
        await this.processModel.updateOne(
          {
            uuid: item.uuid,
          },
          {
            status: AZURE_PROCESS_STATUS.SAVE_AUDIO,
            audio_id: data.resultId,
          },
        );
        this.processInQueue(session_id).then();
      });
    });
  }

  async processInQueue(sessionId: string): Promise<void> {
    const find: any = await this.processModel.findOne({
      session_id: sessionId,
      type: TRANSLATE_PROCESS_TYPE.TRANSLATE,
      status: {
        $ne: AZURE_PROCESS_STATUS.SENT,
      },
    });
    if (!find) {
      return;
    }
    const updateToProcessing: any = await this.processModel.updateOne(
      {
        _id: find._id,
        status: AZURE_PROCESS_STATUS.SAVE_AUDIO,
      },
      {
        status: AZURE_PROCESS_STATUS.SENT,
      },
    );

    if (!updateToProcessing.modifiedCount) {
      return;
    }
    const findAudio = this.dataAudio.get(find.uuid);
    if (!findAudio) {
      return;
    }

    console.log(`${find.no}-${find.no_translate}-${find.uuid}`);
    this.eventData.emit(sessionId, findAudio);
    this.dataAudio.delete(find.uuid);
    await this.processInQueue(sessionId);
  }
}
