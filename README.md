## Installation

```bash
$ pnpm install
```

## Migration: run

```bash
$ pnpm run migration:run
```

## Migration: generate

```bash
$ pnpm run migration:generate
```

## Running the app

```bash
# development
$ pnpm run start

# watch mode
$ pnpm run start:dev

# production mode
$ pnpm run start:prod
```

## Test

```bash
# unit tests
$ pnpm run test

# e2e tests
$ pnpm run test:e2e

# test coverage
$ pnpm run test:cov
```
