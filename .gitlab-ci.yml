container-image-build:
  stage: build
  environment:
    name: development
    url: https://speech-translation-wrtc.dev.locaai.io
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: ['']
  variables:
    DOCKERFILE: dev.Dockerfile
    # IMAGE_NAME: ""
    VERSION: dev
    K<PERSON>IKO_ARGS: '--cache=true --cache-run-layers --cache-copy-layers'
  script:
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"auth\":\"$(echo -n ${CI_REGISTRY_USER}:${CI_REGISTRY_PASSWORD} | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor --context $CI_PROJECT_DIR --dockerfile $CI_PROJECT_DIR/${DOCKERFILE} --destination $CI_REGISTRY_IMAGE:$VERSION $KANIKO_ARGS
  rules:
    - if: $CI_COMMIT_BRANCH == "development"

deploy:dev:
  stage: deploy
  environment:
    name: development
    url: https://speech-translation-wrtc.dev.locaai.io
  needs: ['container-image-build']
  variables:
    MANIFEST_DIR: locaai-speech-translation-wrtc
    DEPLOYMENT_NAME: deployment/locaai-speech-translation-wrtc
    NAMESPACE: locaai-dev
    CI_COMMIT_SHORT_SHA: ${CI_COMMIT_SHORT_SHA}
    CI_PROJECT_TITLE: ${CI_PROJECT_TITLE}
  trigger:
    project: locamos/gcp-infrastructure
    branch: dev
    strategy: depend
  when: on_success
  rules:
    - if: $CI_COMMIT_BRANCH == "development"
